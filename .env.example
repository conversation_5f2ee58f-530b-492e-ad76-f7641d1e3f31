# Google AI API Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Optional: OpenAI API for fallback
OPENAI_API_KEY=your_openai_api_key_here

# PubMed API Configuration (optional - uses free tier by default)
PUBMED_API_KEY=your_pubmed_api_key_here
PUBMED_EMAIL=<EMAIL>

# Application Configuration
APP_NAME=MedInsight-Agent
DEBUG=True
LOG_LEVEL=INFO

# Database Configuration (for ChromaDB)
CHROMA_PERSIST_DIRECTORY=./data/chroma_db

# File Upload Configuration
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=pdf

# Model Configuration
GEMINI_MODEL=gemini-2.5-flash
TEMPERATURE=0.1
MAX_TOKENS=8192
