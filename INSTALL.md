# 📦 Installation Guide for MedInsight-Agent

This guide provides detailed installation instructions for MedInsight-Agent on different operating systems.

## 🖥️ System Requirements

- **Python**: 3.8 or higher
- **RAM**: Minimum 8GB (16GB recommended)
- **Storage**: 5GB free space
- **Internet**: Required for API calls and model downloads

## 🚀 Quick Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd MedicalAgent
```

### 2. Create Virtual Environment
```bash
# Using venv
python -m venv medinsight-env
source medinsight-env/bin/activate  # Linux/Mac
# or
medinsight-env\Scripts\activate     # Windows

# Using conda
conda create -n medinsight python=3.9
conda activate medinsight
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install Additional Components
```bash
# Install spaCy clinical model
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz

# Download NLTK data (if needed)
python -c "import nltk; nltk.download('punkt')"
```

### 5. Setup Environment
```bash
cp .env.example .env
# Edit .env file with your API keys
```

### 6. Run Application
```bash
python run_app.py
```

## 🔧 Platform-Specific Instructions

### Windows

#### Prerequisites
1. **Python 3.8+**: Download from [python.org](https://www.python.org/downloads/)
2. **Git**: Download from [git-scm.com](https://git-scm.com/download/win)
3. **Tesseract OCR**: Download from [GitHub](https://github.com/UB-Mannheim/tesseract/wiki)

#### Installation Steps
```cmd
# Clone repository
git clone <repository-url>
cd MedicalAgent

# Create virtual environment
python -m venv medinsight-env
medinsight-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install spaCy model
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz

# Setup environment
copy .env.example .env
# Edit .env file with your API keys

# Run application
python run_app.py
```

#### Tesseract Setup
1. Download Tesseract installer
2. Install to default location (usually `C:\Program Files\Tesseract-OCR`)
3. Add to PATH: `C:\Program Files\Tesseract-OCR`
4. Verify: `tesseract --version`

### macOS

#### Prerequisites
```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python and dependencies
brew install python@3.9 git tesseract
```

#### Installation Steps
```bash
# Clone repository
git clone <repository-url>
cd MedicalAgent

# Create virtual environment
python3 -m venv medinsight-env
source medinsight-env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install spaCy model
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz

# Setup environment
cp .env.example .env
# Edit .env file with your API keys

# Run application
python run_app.py
```

### Linux (Ubuntu/Debian)

#### Prerequisites
```bash
# Update package list
sudo apt update

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv git tesseract-ocr

# Install additional libraries for PDF processing
sudo apt install poppler-utils
```

#### Installation Steps
```bash
# Clone repository
git clone <repository-url>
cd MedicalAgent

# Create virtual environment
python3 -m venv medinsight-env
source medinsight-env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install spaCy model
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz

# Setup environment
cp .env.example .env
# Edit .env file with your API keys

# Run application
python run_app.py
```

## 🔑 API Key Setup

### Google AI API Key

1. **Visit Google AI Studio**
   - Go to [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account

2. **Create API Key**
   - Click "Create API Key"
   - Choose your project or create a new one
   - Copy the generated API key

3. **Add to Environment**
   ```bash
   # Edit .env file
   GOOGLE_API_KEY=your_api_key_here
   ```

### Optional: OpenAI API Key

1. **Visit OpenAI Platform**
   - Go to [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
   - Sign in to your account

2. **Create API Key**
   - Click "Create new secret key"
   - Copy the generated key

3. **Add to Environment**
   ```bash
   # Edit .env file
   OPENAI_API_KEY=your_openai_key_here
   ```

## 🧪 Verification

### Test Installation
```bash
# Run basic tests
python run_app.py test

# Run example
python run_app.py example

# Check dependencies
python -c "import streamlit, crewai, langchain; print('✅ All dependencies installed')"
```

### Test OCR
```bash
# Test Tesseract
tesseract --version

# Test in Python
python -c "import pytesseract; print('✅ OCR working')"
```

### Test API Connection
```bash
# Test Google AI
python -c "
import google.generativeai as genai
from config import Config
config = Config()
genai.configure(api_key=config.GOOGLE_API_KEY)
print('✅ Google AI API connected')
"
```

## 🐳 Docker Installation (Alternative)

### Using Docker
```bash
# Build image
docker build -t medinsight-agent .

# Run container
docker run -p 8501:8501 -e GOOGLE_API_KEY=your_key medinsight-agent
```

### Docker Compose
```bash
# Create docker-compose.yml with your API keys
docker-compose up
```

## 🔧 Troubleshooting

### Common Issues

1. **"No module named 'src'"**
   ```bash
   # Add to PYTHONPATH
   export PYTHONPATH="${PYTHONPATH}:$(pwd)"
   ```

2. **Tesseract not found**
   ```bash
   # Windows: Add to PATH
   # Linux: sudo apt install tesseract-ocr
   # macOS: brew install tesseract
   ```

3. **spaCy model not found**
   ```bash
   # Reinstall clinical model
   pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz
   ```

4. **Memory errors**
   ```bash
   # Increase virtual memory
   # Use smaller batch sizes
   # Process PDFs individually
   ```

### Performance Optimization

1. **GPU Acceleration**
   ```bash
   # Install CUDA version of PyTorch
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

2. **Faster Dependencies**
   ```bash
   # Use optimized BLAS
   pip install numpy[mkl]
   ```

## 📞 Support

If you encounter issues:

1. Check the [Troubleshooting](#-troubleshooting) section
2. Review system requirements
3. Verify API key configuration
4. Check GitHub Issues
5. Create a new issue with:
   - Operating system
   - Python version
   - Error messages
   - Steps to reproduce

## 🔄 Updates

To update MedInsight-Agent:

```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt --upgrade

# Update spaCy model if needed
pip install --upgrade https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz
```
