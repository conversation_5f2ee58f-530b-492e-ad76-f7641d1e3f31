# 🏥 MedInsight-Agent

**AI-Powered Medical Report Analysis System**

MedInsight-Agent is a comprehensive agentic AI pipeline that uses CrewAI, LangChain, and Gemini-2.5-Flash to analyze medical reports. Upload a PDF medical report and get structured clinical insights, interpretations, and evidence-based recommendations.

## ✨ Features

- **🔍 Advanced PDF Processing**: Multi-method text extraction (PyMuPDF + pdfminer)
- **🧠 Clinical NLP**: Extract medications, conditions, lab values, and vital signs
- **🤖 AI-Powered Interpretation**: Clinical analysis using Gemini-2.5-Flash
- **👥 Multi-Agent Architecture**: CrewAI orchestration with specialized agents
- **📚 RAG Integration**: LangChain-powered retrieval of clinical guidelines
- **🌐 User-Friendly Interface**: Streamlit web application
- **📊 Comprehensive Analytics**: Confidence scoring and detailed reporting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│  Medical Pipeline │────│  CrewAI Agents  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼────┐ ┌────▼────┐ ┌───▼──────┐
            │ PDF Processor│ │Clinical │ │ Gemini   │
            │ (Multi-method)│ │   NLP   │ │ Client   │
            └────────────┘ └─────────┘ └──────────┘
                                │
                    ┌───────────▼───────────┐
                    │   LangChain + RAG     │
                    │  (Memory & PubMed)    │
                    └───────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Google AI API Key (for Gemini-2.5-Flash)

### Installation

1. **Install dependencies**
```bash
pip install -r requirements.txt
```

2. **Install spaCy clinical model**
```bash
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz
```

3. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env and add your Google API key
```

### Running the Application

1. **Start the Streamlit app**
```bash
streamlit run app.py
```

2. **Open your browser** to `http://localhost:8501`

3. **Upload a medical PDF** and analyze!

## 📋 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_google_api_key_here



### Getting API Keys

1. **Google AI API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## 💻 Usage Examples

### Basic Usage (Python)

```python
from src.pipeline.medical_pipeline import MedicalPipeline

# Initialize pipeline
pipeline = MedicalPipeline()

# Process a PDF file
with open("medical_report.pdf", "rb") as f:
    pdf_content = f.read()

result = pipeline.process_medical_report(pdf_content)

if result.success:
    print(f"Analysis complete! Confidence: {result.confidence_score:.2%}")
    print(f"Summary: {result.clinical_interpretation['summary']}")
else:
    print(f"Error: {result.error_message}")
```

### Advanced Usage with Context

```python
# Add patient context
patient_context = {
    "age": 45,
    "gender": "Male",
    "medical_history": ["Hypertension", "Diabetes"]
}

result = pipeline.process_medical_report(
    pdf_content=pdf_content,
    patient_context=patient_context,
    processing_mode="comprehensive"  # or "fast", "research"
)
```



## 📁 Project Structure

```
MedicalAgent/
├── src/
│   ├── agents/          # CrewAI agents
│   ├── chains/          # LangChain chains
│   ├── models/          # AI model clients
│   ├── pipeline/        # Main orchestration
│   ├── tools/           # PDF, NLP, PubMed tools
│   └── utils/           # Utilities

├── data/                # Data storage
├── app.py              # Streamlit application
├── config.py           # Configuration
└── requirements.txt    # Dependencies
```

## 🔧 Components

### 1. PDF Processing Agent
- **Tools**: PyMuPDF, pdfminer
- **Function**: Extract text from medical PDFs
- **Features**: Multi-method extraction, intelligent fallback

### 2. Clinical NLP Agent
- **Tools**: spaCy, scispacy, Clinical BERT
- **Function**: Extract clinical entities
- **Entities**: Medications, conditions, lab values, vital signs

### 3. Knowledge Research Agent
- **Tools**: Local Knowledge Base
- **Function**: Retrieve clinical guidelines
- **Features**: Local knowledge retrieval, relevance scoring

### 4. Clinical Interpretation Agent
- **Tools**: Gemini-2.5-Flash
- **Function**: Generate clinical insights
- **Output**: Structured interpretation with recommendations

## 🎯 Processing Modes

- **Fast**: Basic extraction and interpretation
- **Comprehensive**: Full pipeline with entity extraction
- **Research**: Includes literature retrieval and RAG

## 📊 Output Format

```json
{
  "success": true,
  "processing_time": 15.2,
  "confidence_score": 0.87,
  "clinical_interpretation": {
    "summary": "Patient presents with...",
    "abnormalities": {
      "critical": ["Elevated troponin"],
      "significant": ["Hypertension"],
      "minor": []
    },
    "differential_diagnoses": ["Acute MI", "Unstable angina"],
    "recommendations": ["Immediate cardiology consult"],
    "follow_up_tests": ["Serial ECGs", "Echo"],
    "clinical_significance": "High risk presentation"
  },
  "recommendations": ["Emergency intervention required"]
}
```

## 🔒 Privacy & Security

- **Local Processing**: All analysis runs locally
- **No Data Storage**: PDFs are processed in memory
- **HIPAA Considerations**: Suitable for local deployment
- **API Security**: Secure API key management

## 🚨 Limitations & Disclaimers

⚠️ **IMPORTANT MEDICAL DISCLAIMER**

This tool is for **educational and research purposes only**. It is **NOT** intended for:
- Clinical decision making
- Patient diagnosis or treatment
- Replacing professional medical judgment

Always consult qualified healthcare professionals for medical decisions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: GitHub Issues
- **Documentation**: See `/docs` folder
- **Examples**: See `/examples` folder

## 🔄 Updates

- **v1.0.0**: Initial release with full pipeline
- **Roadmap**: FHIR integration, additional models, cloud deployment

## 🛠️ Troubleshooting

### Common Issues

1. **"GOOGLE_API_KEY not found"**
   - Ensure you've created a `.env` file with your API key
   - Check that the key is valid and has proper permissions

2. **spaCy model not found**
   - Install the clinical model: `pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz`

3. **Memory issues with large PDFs**
   - Reduce PDF size or split into smaller files
   - Increase system memory allocation

### Performance Tips

- Use "fast" mode for quick analysis
- Enable GPU acceleration for transformers
- Use SSD storage for better I/O performance

---

**Built with ❤️ for the medical AI community**
