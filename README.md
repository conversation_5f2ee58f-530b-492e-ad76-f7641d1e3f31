# 🏥 MedInsight-Agent

**AI-Powered Medical Report Analysis System**

MedInsight-Agent is a comprehensive agentic AI pipeline that uses CrewAI, LangChain, and Gemini-2.5-Flash to analyze medical reports. Upload a PDF medical report and get structured clinical insights, interpretations, and evidence-based recommendations.

## ✨ Features

- **🔍 Advanced PDF Processing**: Multi-method text extraction with OCR fallback
- **🧠 Clinical NLP**: Extract medications, conditions, lab values, and vital signs
- **🤖 AI-Powered Interpretation**: Clinical analysis using Gemini-2.5-Flash
- **👥 Multi-Agent Architecture**: CrewAI orchestration with specialized agents
- **📚 RAG Integration**: LangChain-powered retrieval of clinical guidelines
- **🌐 User-Friendly Interface**: Streamlit web application
- **📊 Comprehensive Analytics**: Confidence scoring and detailed reporting

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│  Medical Pipeline │────│  CrewAI Agents  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼────┐ ┌────▼────┐ ┌───▼──────┐
            │ PDF Processor│ │Clinical │ │ Gemini   │
            │   + OCR     │ │   NLP   │ │ Client   │
            └────────────┘ └─────────┘ └──────────┘
                                │
                    ┌───────────▼───────────┐
                    │   LangChain + RAG     │
                    │  (Memory & PubMed)    │
                    └───────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Google AI API Key (for Gemini-2.5-Flash)
- Tesseract OCR (for scanned PDFs)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd MedicalAgent
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Install spaCy clinical model**
```bash
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz
```

4. **Install Tesseract OCR**
   - **Windows**: Download from [GitHub](https://github.com/UB-Mannheim/tesseract/wiki)
   - **macOS**: `brew install tesseract`
   - **Linux**: `sudo apt-get install tesseract-ocr`

5. **Set up environment variables**
```bash
cp .env.example .env
# Edit .env and add your Google API key
```

### Running the Application

1. **Start the Streamlit app**
```bash
streamlit run app.py
```

2. **Open your browser** to `http://localhost:8501`

3. **Upload a medical PDF** and analyze!

## 📋 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_google_api_key_here

# Optional
OPENAI_API_KEY=your_openai_api_key_here
PUBMED_EMAIL=<EMAIL>
DEBUG=True
LOG_LEVEL=INFO
```

### Getting API Keys

1. **Google AI API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## 💻 Usage Examples

### Basic Usage (Python)

```python
from src.pipeline.medical_pipeline import MedicalPipeline

# Initialize pipeline
pipeline = MedicalPipeline()

# Process a PDF file
with open("medical_report.pdf", "rb") as f:
    pdf_content = f.read()

result = pipeline.process_medical_report(pdf_content)

if result.success:
    print(f"Analysis complete! Confidence: {result.confidence_score:.2%}")
    print(f"Summary: {result.clinical_interpretation['summary']}")
else:
    print(f"Error: {result.error_message}")
```

### Advanced Usage with Context

```python
# Add patient context
patient_context = {
    "age": 45,
    "gender": "Male",
    "medical_history": ["Hypertension", "Diabetes"]
}

result = pipeline.process_medical_report(
    pdf_content=pdf_content,
    patient_context=patient_context,
    processing_mode="comprehensive"  # or "fast", "research"
)
```

### Running Examples

```bash
python examples/example_usage.py
```

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=src --cov-report=html
```

## 📁 Project Structure

```
MedicalAgent/
├── src/
│   ├── agents/          # CrewAI agents
│   ├── chains/          # LangChain chains
│   ├── models/          # AI model clients
│   ├── pipeline/        # Main orchestration
│   ├── tools/           # PDF, NLP, PubMed tools
│   └── utils/           # Utilities
├── tests/               # Unit tests
├── examples/            # Usage examples
├── data/                # Data storage
├── app.py              # Streamlit application
├── config.py           # Configuration
└── requirements.txt    # Dependencies
```

## 🔧 Components

### 1. PDF Processing Agent
- **Tools**: PyMuPDF, pdfminer, Tesseract OCR
- **Function**: Extract text from medical PDFs
- **Features**: Multi-method extraction, OCR fallback

### 2. Clinical NLP Agent
- **Tools**: spaCy, scispacy, Clinical BERT
- **Function**: Extract clinical entities
- **Entities**: Medications, conditions, lab values, vital signs

### 3. Literature Research Agent
- **Tools**: PubMed API
- **Function**: Retrieve clinical guidelines
- **Features**: Relevance scoring, quality filtering

### 4. Clinical Interpretation Agent
- **Tools**: Gemini-2.5-Flash
- **Function**: Generate clinical insights
- **Output**: Structured interpretation with recommendations

## 🎯 Processing Modes

- **Fast**: Basic extraction and interpretation
- **Comprehensive**: Full pipeline with entity extraction
- **Research**: Includes literature retrieval and RAG

## 📊 Output Format

```json
{
  "success": true,
  "processing_time": 15.2,
  "confidence_score": 0.87,
  "clinical_interpretation": {
    "summary": "Patient presents with...",
    "abnormalities": {
      "critical": ["Elevated troponin"],
      "significant": ["Hypertension"],
      "minor": []
    },
    "differential_diagnoses": ["Acute MI", "Unstable angina"],
    "recommendations": ["Immediate cardiology consult"],
    "follow_up_tests": ["Serial ECGs", "Echo"],
    "clinical_significance": "High risk presentation"
  },
  "recommendations": ["Emergency intervention required"]
}
```

## 🔒 Privacy & Security

- **Local Processing**: All analysis runs locally
- **No Data Storage**: PDFs are processed in memory
- **HIPAA Considerations**: Suitable for local deployment
- **API Security**: Secure API key management

## 🚨 Limitations & Disclaimers

⚠️ **IMPORTANT MEDICAL DISCLAIMER**

This tool is for **educational and research purposes only**. It is **NOT** intended for:
- Clinical decision making
- Patient diagnosis or treatment
- Replacing professional medical judgment

Always consult qualified healthcare professionals for medical decisions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: GitHub Issues
- **Documentation**: See `/docs` folder
- **Examples**: See `/examples` folder

## 🔄 Updates

- **v1.0.0**: Initial release with full pipeline
- **Roadmap**: FHIR integration, additional models, cloud deployment

## 🛠️ Troubleshooting

### Common Issues

1. **"GOOGLE_API_KEY not found"**
   - Ensure you've created a `.env` file with your API key
   - Check that the key is valid and has proper permissions

2. **OCR not working**
   - Install Tesseract OCR for your operating system
   - Verify Tesseract is in your system PATH

3. **spaCy model not found**
   - Install the clinical model: `pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz`

4. **Memory issues with large PDFs**
   - Reduce PDF size or split into smaller files
   - Increase system memory allocation

### Performance Tips

- Use "fast" mode for quick analysis
- Enable GPU acceleration for transformers
- Use SSD storage for better I/O performance

---

**Built with ❤️ for the medical AI community**
