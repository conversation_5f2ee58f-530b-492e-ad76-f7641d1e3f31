# 🏥 MedInsight-Agent

**AI-Powered Medical Report Analysis System**

MedInsight-Agent is a comprehensive agentic AI pipeline that uses CrewAI, LangChain, and Gemini-2.5-Flash to analyze medical reports. Upload a PDF medical report and get structured clinical insights, interpretations, and evidence-based recommendations.

## 🏗️ Project Architecture

This project implements a multi-agent AI system for medical report analysis with the following architecture:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│  Medical Pipeline │────│  CrewAI Agents  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │           │           │
            ┌───────▼────┐ ┌────▼────┐ ┌───▼──────┐
            │ PDF Processor│ │Clinical │ │ Gemini   │
            │ (Multi-method)│ │   NLP   │ │ Client   │
            └────────────┘ └─────────┘ └──────────┘
                                │
                    ┌───────────▼───────────┐
                    │   LangChain + RAG     │
                    │    (Memory & KB)      │
                    └───────────────────────┘
```

## 📁 Project Structure & Directory Explanation

```
MedicalAgent/
├── src/                          # Main source code directory
│   ├── agents/                   # CrewAI agent implementations
│   │   ├── __init__.py          # Package initialization
│   │   └── medical_agents.py    # Four specialized AI agents:
│   │                            #   - PDF Processing Agent
│   │                            #   - Clinical NLP Agent
│   │                            #   - Knowledge Research Agent
│   │                            #   - Clinical Interpretation Agent
│   │
│   ├── chains/                   # LangChain integration
│   │   ├── __init__.py          # Package initialization
│   │   └── medical_chains.py    # LangChain chains for:
│   │                            #   - PDF content processing
│   │                            #   - Clinical data analysis
│   │                            #   - Recommendation generation
│   │                            #   - Memory management & RAG
│   │
│   ├── models/                   # AI model clients
│   │   ├── __init__.py          # Package initialization
│   │   └── gemini_client.py     # Gemini-2.5-Flash integration:
│   │                            #   - Clinical interpretation
│   │                            #   - Lab value analysis
│   │                            #   - Clinical summary generation
│   │
│   ├── pipeline/                 # Main orchestration
│   │   ├── __init__.py          # Package initialization
│   │   └── medical_pipeline.py  # Core pipeline orchestrator:
│   │                            #   - Coordinates all agents
│   │                            #   - Manages processing flow
│   │                            #   - Handles error recovery
│   │                            #   - Confidence scoring
│   │
│   ├── tools/                    # Processing tools
│   │   ├── __init__.py          # Package initialization
│   │   ├── pdf_processor.py     # PDF text extraction:
│   │   │                        #   - PyMuPDF primary extraction
│   │   │                        #   - pdfminer fallback
│   │   │                        #   - Text cleaning & formatting
│   │   └── clinical_nlp.py      # Medical NLP processing:
│   │                            #   - spaCy clinical model
│   │                            #   - Medical entity extraction
│   │                            #   - Lab values & vital signs
│   │                            #   - Structured data conversion
│   │
│   └── utils/                    # Utility functions
│       ├── __init__.py          # Package initialization
│       └── logging_config.py    # Logging configuration:
│                                #   - Application logging setup
│                                #   - Log rotation & formatting
│
├── data/                         # Data storage (auto-created)
│   ├── uploads/                 # Temporary PDF storage
│   ├── chroma_db/              # Vector database for RAG
│   ├── models/                 # Downloaded model cache
│   └── logs/                   # Application logs
│
├── app.py                       # Streamlit web application:
│                                #   - PDF upload interface
│                                #   - Results visualization
│                                #   - Interactive dashboard
│                                #   - System status monitoring
│
├── config.py                    # Configuration management:
│                                #   - Environment variables
│                                #   - API keys handling
│                                #   - Directory setup
│                                #   - Model parameters
│
├── requirements.txt             # Python dependencies
├── .env.example                # Environment template
├── .gitignore                  # Git ignore rules
└── README.md                   # This documentation
```

## 🔧 Core Components Explained

### 1. **src/agents/medical_agents.py**
- **PDF Processing Agent**: Extracts text from medical PDFs using PyMuPDF and pdfminer
- **Clinical NLP Agent**: Uses spaCy clinical models to extract medical entities
- **Knowledge Research Agent**: Searches local knowledge base for clinical guidelines
- **Clinical Interpretation Agent**: Uses Gemini-2.5-Flash for medical analysis

### 2. **src/tools/clinical_nlp.py**
- **Medical Entity Recognition**: Extracts medications, conditions, procedures, anatomy
- **Lab Values Extraction**: Identifies laboratory results with regex patterns
- **Vital Signs Processing**: Extracts blood pressure, heart rate, temperature, etc.
- **Structured Data Conversion**: Converts unstructured text to JSON format

### 3. **src/models/gemini_client.py**
- **Clinical Interpretation**: Analyzes medical data and provides insights
- **Abnormality Detection**: Identifies critical, significant, and minor findings
- **Differential Diagnosis**: Suggests possible diagnoses with reasoning
- **Recommendations**: Generates evidence-based treatment recommendations

### 4. **src/pipeline/medical_pipeline.py**
- **Orchestration**: Coordinates all agents and processing steps
- **Error Handling**: Manages failures and provides fallback mechanisms
- **Confidence Scoring**: Calculates reliability scores for analysis
- **Processing Modes**: Supports fast, comprehensive, and research modes

### 5. **app.py (Streamlit Interface)**
- **PDF Upload**: Drag-and-drop interface for medical reports
- **Real-time Processing**: Shows progress and status updates
- **Results Dashboard**: Visualizes analysis results and metrics
- **Interactive Analysis**: Detailed breakdown of findings and recommendations

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- Google AI API Key (for Gemini-2.5-Flash)

### Installation Commands
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Install spaCy clinical model
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.4/en_core_sci_sm-0.5.4.tar.gz

# 3. Set up environment
cp .env.example .env
# Edit .env and add your Google API key

# 4. Run the application
streamlit run app.py
```

### Environment Configuration
Create a `.env` file with:
```env
GOOGLE_API_KEY=your_google_api_key_here
```

Get your Google AI API Key from [Google AI Studio](https://makersuite.google.com/app/apikey)



## 🎯 Processing Flow

### 1. **PDF Upload** (app.py)
- User uploads medical PDF through Streamlit interface
- File validation and size checking
- Temporary storage in memory

### 2. **Text Extraction** (src/tools/pdf_processor.py)
- Primary: PyMuPDF extraction
- Fallback: pdfminer for complex layouts
- Text cleaning and formatting

### 3. **Clinical NLP** (src/tools/clinical_nlp.py)
- spaCy clinical model processes text
- Extracts: medications, conditions, lab values, vital signs
- Converts to structured JSON format

### 4. **AI Analysis** (src/models/gemini_client.py)
- Gemini-2.5-Flash analyzes structured data
- Identifies abnormalities and clinical significance
- Generates differential diagnoses and recommendations

### 5. **Results Display** (app.py)
- Interactive dashboard with visualizations
- Confidence scoring and detailed breakdowns
- Downloadable analysis reports

## 🔄 Processing Modes

- **Fast**: Basic extraction and interpretation
- **Comprehensive**: Full pipeline with entity extraction
- **Research**: Includes knowledge base retrieval and RAG

## 📊 Key Technologies

### **CrewAI Framework**
- Multi-agent orchestration system
- Specialized agents for different medical tasks
- Sequential task execution with error handling

### **LangChain Integration**
- Memory management for conversation context
- RAG (Retrieval Augmented Generation) capabilities
- Vector database integration with ChromaDB

### **Gemini-2.5-Flash Model**
- Google's latest medical-capable AI model
- Clinical interpretation and analysis
- Evidence-based recommendation generation

### **spaCy Clinical NLP**
- Medical entity recognition and extraction
- Clinical terminology understanding
- Biomedical text processing capabilities

### **Streamlit Interface**
- Interactive web application
- Real-time processing visualization
- Responsive dashboard design

## ⚠️ Important Notes

**Medical Disclaimer**: This tool is for educational and research purposes only. Not intended for clinical decision making, patient diagnosis, or treatment. Always consult qualified healthcare professionals for medical decisions.

**Privacy**: All processing runs locally. PDFs are processed in memory only. No data is stored or transmitted to external servers (except Google AI API calls).
