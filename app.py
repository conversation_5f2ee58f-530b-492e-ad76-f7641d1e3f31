"""
MedInsight-Agent: Simple Medical Report Analyzer
A streamlined single-file application for analyzing medical reports using AI
"""
import streamlit as st
import json
import time
import re
import io
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try to import optional dependencies
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    from pdfminer.high_level import extract_text
    PDFMINER_AVAILABLE = True
except ImportError:
    PDFMINER_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

# Configuration
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
GEMINI_MODEL = "gemini-2.5-flash"

@dataclass
class AnalysisResult:
    """Simple data class for analysis results"""
    success: bool
    text_extracted: str
    entities: Dict[str, List[str]]
    interpretation: str
    recommendations: List[str]
    confidence: float
    error_message: str = ""

# Page configuration
st.set_page_config(
    page_title="MedInsight-Agent",
    page_icon="🏥",
    layout="wide"
)

# Simple CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 1rem;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# ============================================================================
# SIMPLE PDF PROCESSOR
# ============================================================================

def extract_text_from_pdf(pdf_content: bytes) -> str:
    """Extract text from PDF using available libraries"""

    # Try PyMuPDF first
    if PYMUPDF_AVAILABLE:
        try:
            doc = fitz.open(stream=pdf_content, filetype="pdf")
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            if text.strip():
                return text.strip()
        except Exception as e:
            st.warning(f"PyMuPDF failed: {e}")

    # Try pdfminer as fallback
    if PDFMINER_AVAILABLE:
        try:
            text = extract_text(io.BytesIO(pdf_content))
            if text.strip():
                return text.strip()
        except Exception as e:
            st.warning(f"pdfminer failed: {e}")

    # If no PDF libraries available
    return "PDF processing libraries not available. Please install PyMuPDF: pip install PyMuPDF"

# ============================================================================
# SIMPLE CLINICAL NLP
# ============================================================================

def extract_medical_entities(text: str) -> Dict[str, List[str]]:
    """Extract medical entities using regex patterns"""

    entities = {
        "medications": [],
        "conditions": [],
        "lab_values": [],
        "vital_signs": []
    }

    # Medication patterns
    med_patterns = [
        r'(?i)(aspirin|metoprolol|lisinopril|atorvastatin|metformin)\s*(\d+\s*mg)',
        r'(?i)(insulin|warfarin|digoxin|furosemide|prednisone)\s*(\d+\s*mg|units)',
    ]

    for pattern in med_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple):
                entities["medications"].append(f"{match[0]} {match[1]}")
            else:
                entities["medications"].append(match)

    # Condition patterns
    condition_patterns = [
        r'(?i)(hypertension|diabetes|chest pain|shortness of breath|fever)',
        r'(?i)(pneumonia|bronchitis|asthma|copd|heart failure)',
        r'(?i)(myocardial infarction|stroke|seizure|depression|anxiety)'
    ]

    for pattern in condition_patterns:
        matches = re.findall(pattern, text)
        entities["conditions"].extend(matches)

    # Lab values
    lab_patterns = [
        r'(?i)(glucose|cholesterol|hemoglobin|creatinine)\s*:?\s*(\d+\.?\d*)\s*(mg/dl|g/dl)?',
        r'(?i)(sodium|potassium|chloride)\s*:?\s*(\d+\.?\d*)\s*(meq/l)?'
    ]

    for pattern in lab_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple) and len(match) >= 2:
                entities["lab_values"].append(f"{match[0]}: {match[1]} {match[2] if len(match) > 2 else ''}")

    # Vital signs
    vital_patterns = [
        r'(?i)(blood pressure|bp)\s*:?\s*(\d+/\d+)\s*(mmhg)?',
        r'(?i)(heart rate|pulse)\s*:?\s*(\d+)\s*(bpm)?',
        r'(?i)(temperature|temp)\s*:?\s*(\d+\.?\d*)\s*(°?f|°?c)?'
    ]

    for pattern in vital_patterns:
        matches = re.findall(pattern, text)
        for match in matches:
            if isinstance(match, tuple) and len(match) >= 2:
                entities["vital_signs"].append(f"{match[0]}: {match[1]} {match[2] if len(match) > 2 else ''}")

    return entities

# ============================================================================
# SIMPLE AI ANALYSIS
# ============================================================================

def analyze_with_gemini(text: str, entities: Dict[str, List[str]]) -> Dict[str, Any]:
    """Analyze medical data using Gemini AI"""

    if not GEMINI_AVAILABLE or not GOOGLE_API_KEY:
        return {
            "interpretation": "AI analysis not available. Please install google-generativeai and set GOOGLE_API_KEY.",
            "recommendations": ["Manual review recommended"],
            "confidence": 0.0
        }

    try:
        genai.configure(api_key=GOOGLE_API_KEY)
        model = genai.GenerativeModel(GEMINI_MODEL)

        prompt = f"""
You are a medical AI assistant. Analyze this medical report and provide insights.

EXTRACTED TEXT:
{text[:2000]}...

EXTRACTED ENTITIES:
Medications: {', '.join(entities.get('medications', []))}
Conditions: {', '.join(entities.get('conditions', []))}
Lab Values: {', '.join(entities.get('lab_values', []))}
Vital Signs: {', '.join(entities.get('vital_signs', []))}

Please provide:
1. A brief clinical interpretation (2-3 sentences)
2. Key recommendations (3-5 bullet points)
3. Any concerning findings that need attention

Keep your response concise and professional.
"""

        response = model.generate_content(prompt)

        # Parse response into sections
        response_text = response.text

        # Simple parsing - look for sections
        interpretation = response_text
        recommendations = [
            "Follow up with healthcare provider",
            "Monitor vital signs",
            "Continue current medications as prescribed"
        ]

        # Try to extract recommendations if formatted properly
        if "recommendations:" in response_text.lower():
            parts = response_text.lower().split("recommendations:")
            if len(parts) > 1:
                rec_text = parts[1]
                # Extract bullet points
                rec_lines = [line.strip() for line in rec_text.split('\n') if line.strip()]
                if rec_lines:
                    recommendations = rec_lines[:5]  # Take first 5

        return {
            "interpretation": interpretation,
            "recommendations": recommendations,
            "confidence": 0.8
        }

    except Exception as e:
        return {
            "interpretation": f"AI analysis failed: {str(e)}",
            "recommendations": ["Manual review recommended"],
            "confidence": 0.0
        }

# ============================================================================
# MAIN PROCESSING FUNCTION
# ============================================================================

def process_medical_report(pdf_content: bytes) -> AnalysisResult:
    """Main function to process medical report"""

    try:
        # Step 1: Extract text from PDF
        st.info("📄 Extracting text from PDF...")
        text = extract_text_from_pdf(pdf_content)

        if not text or len(text) < 50:
            return AnalysisResult(
                success=False,
                text_extracted="",
                entities={},
                interpretation="",
                recommendations=[],
                confidence=0.0,
                error_message="Could not extract meaningful text from PDF"
            )

        # Step 2: Extract medical entities
        st.info("🔍 Extracting medical entities...")
        entities = extract_medical_entities(text)

        # Step 3: AI analysis
        st.info("🧠 Analyzing with AI...")
        ai_analysis = analyze_with_gemini(text, entities)

        return AnalysisResult(
            success=True,
            text_extracted=text,
            entities=entities,
            interpretation=ai_analysis["interpretation"],
            recommendations=ai_analysis["recommendations"],
            confidence=ai_analysis["confidence"]
        )

    except Exception as e:
        return AnalysisResult(
            success=False,
            text_extracted="",
            entities={},
            interpretation="",
            recommendations=[],
            confidence=0.0,
            error_message=str(e)
        )
# ============================================================================
# STREAMLIT UI
# ============================================================================

def main():
    """Main Streamlit application"""

    # Header
    st.markdown('<h1 class="main-header">🏥 MedInsight-Agent</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #7f8c8d;">Simple AI-Powered Medical Report Analysis</p>', unsafe_allow_html=True)

    # Check dependencies
    st.sidebar.header("📋 System Status")

    if GOOGLE_API_KEY:
        st.sidebar.success("✅ Google API Key configured")
    else:
        st.sidebar.error("❌ Google API Key missing")
        st.sidebar.info("Add GOOGLE_API_KEY to your .env file")

    if PYMUPDF_AVAILABLE:
        st.sidebar.success("✅ PyMuPDF available")
    elif PDFMINER_AVAILABLE:
        st.sidebar.success("✅ pdfminer available")
    else:
        st.sidebar.warning("⚠️ No PDF libraries available")
        st.sidebar.info("Install: pip install PyMuPDF")

    if GEMINI_AVAILABLE:
        st.sidebar.success("✅ Gemini AI available")
    else:
        st.sidebar.error("❌ Gemini AI not available")
        st.sidebar.info("Install: pip install google-generativeai")

    # Main content
    st.header("📄 Upload Medical Report")

    uploaded_file = st.file_uploader(
        "Choose a PDF medical report",
        type=['pdf'],
        help="Upload a medical report in PDF format for AI analysis"
    )

    if uploaded_file is not None:
        # Display file info
        col1, col2 = st.columns(2)
        with col1:
            st.metric("File Name", uploaded_file.name)
        with col2:
            st.metric("File Size", f"{uploaded_file.size / 1024:.1f} KB")

        # Process button
        if st.button("🔬 Analyze Report", type="primary"):

            # Read file content
            pdf_content = uploaded_file.read()

            # Process with progress
            with st.spinner("Processing medical report..."):
                result = process_medical_report(pdf_content)

            # Display results
            if result.success:
                st.markdown('<div class="success-box">', unsafe_allow_html=True)
                st.success("✅ Analysis completed successfully!")
                st.markdown('</div>', unsafe_allow_html=True)

                # Confidence score
                st.metric("Confidence Score", f"{result.confidence:.1%}")

                # Extracted entities
                st.subheader("🔍 Extracted Medical Information")

                col1, col2 = st.columns(2)

                with col1:
                    if result.entities.get("medications"):
                        st.write("**💊 Medications:**")
                        for med in result.entities["medications"]:
                            st.write(f"• {med}")

                    if result.entities.get("conditions"):
                        st.write("**🏥 Medical Conditions:**")
                        for condition in result.entities["conditions"]:
                            st.write(f"• {condition}")

                with col2:
                    if result.entities.get("lab_values"):
                        st.write("**🧪 Lab Values:**")
                        for lab in result.entities["lab_values"]:
                            st.write(f"• {lab}")

                    if result.entities.get("vital_signs"):
                        st.write("**❤️ Vital Signs:**")
                        for vital in result.entities["vital_signs"]:
                            st.write(f"• {vital}")

                # AI Interpretation
                st.subheader("🧠 AI Clinical Interpretation")
                st.write(result.interpretation)

                # Recommendations
                st.subheader("📋 Recommendations")
                for i, rec in enumerate(result.recommendations, 1):
                    st.write(f"{i}. {rec}")

                # Show extracted text (expandable)
                with st.expander("📄 View Extracted Text"):
                    st.text_area("Extracted Text", result.text_extracted, height=300)

            else:
                st.markdown('<div class="error-box">', unsafe_allow_html=True)
                st.error(f"❌ Analysis failed: {result.error_message}")
                st.markdown('</div>', unsafe_allow_html=True)

    # Instructions
    st.markdown("---")
    st.subheader("📖 How to Use")
    st.write("""
    1. **Upload a PDF**: Click the upload button and select a medical report PDF
    2. **Analyze**: Click the "Analyze Report" button to process the document
    3. **Review Results**: View extracted medical information and AI analysis
    4. **Get Insights**: Read the clinical interpretation and recommendations

    **Note**: This tool is for educational purposes only. Always consult healthcare professionals for medical decisions.
    """)

if __name__ == "__main__":
    main()
