"""
Streamlit UI for MedInsight-Agent
"""
import streamlit as st
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

from src.pipeline.medical_pipeline import MedicalP<PERSON>eline, ProcessingResult
from config import Config

# Page configuration
st.set_page_config(
    page_title="MedInsight-Agent",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.25rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.25rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class MedInsightApp:
    """Main Streamlit application class"""
    
    def __init__(self):
        self.config = Config()
        self.pipeline = None
        self._initialize_session_state()
    
    def _initialize_session_state(self):
        """Initialize Streamlit session state"""
        if 'processing_results' not in st.session_state:
            st.session_state.processing_results = []
        if 'current_result' not in st.session_state:
            st.session_state.current_result = None
        if 'pipeline_initialized' not in st.session_state:
            st.session_state.pipeline_initialized = False
    
    def _initialize_pipeline(self, use_crewai: bool, use_langchain: bool):
        """Initialize the medical pipeline"""
        if not st.session_state.pipeline_initialized:
            with st.spinner("Initializing AI pipeline..."):
                try:
                    self.pipeline = MedicalPipeline(
                        use_crewai=use_crewai,
                        use_langchain=use_langchain
                    )
                    st.session_state.pipeline_initialized = True
                    st.success("✅ Pipeline initialized successfully!")
                except Exception as e:
                    st.error(f"❌ Error initializing pipeline: {str(e)}")
                    return False
        return True
    
    def run(self):
        """Main application entry point"""
        # Header
        st.markdown('<h1 class="main-header">🏥 MedInsight-Agent</h1>', unsafe_allow_html=True)
        st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #7f8c8d;">AI-Powered Medical Report Analysis</p>', unsafe_allow_html=True)
        
        # Sidebar configuration
        self._render_sidebar()
        
        # Main content
        tab1, tab2, tab3, tab4 = st.tabs(["📄 Upload & Analyze", "📊 Results Dashboard", "🔍 Detailed Analysis", "⚙️ System Status"])
        
        with tab1:
            self._render_upload_tab()
        
        with tab2:
            self._render_dashboard_tab()
        
        with tab3:
            self._render_analysis_tab()
        
        with tab4:
            self._render_status_tab()
    
    def _render_sidebar(self):
        """Render sidebar with configuration options"""
        st.sidebar.header("⚙️ Configuration")
        
        # Pipeline options
        st.sidebar.subheader("AI Pipeline Options")
        use_crewai = st.sidebar.checkbox("Use CrewAI Agents", value=True, help="Enable multi-agent processing")
        use_langchain = st.sidebar.checkbox("Use LangChain RAG", value=True, help="Enable memory and retrieval")
        
        # Processing mode
        processing_mode = st.sidebar.selectbox(
            "Processing Mode",
            ["fast", "comprehensive", "research"],
            index=1,
            help="Choose processing depth"
        )
        
        # Initialize pipeline button
        if st.sidebar.button("🚀 Initialize Pipeline"):
            self._initialize_pipeline(use_crewai, use_langchain)
        
        # Store settings in session state
        st.session_state.use_crewai = use_crewai
        st.session_state.use_langchain = use_langchain
        st.session_state.processing_mode = processing_mode
        
        # Patient context
        st.sidebar.subheader("👤 Patient Context (Optional)")
        patient_age = st.sidebar.number_input("Age", min_value=0, max_value=120, value=0)
        patient_gender = st.sidebar.selectbox("Gender", ["", "Male", "Female", "Other"])
        
        patient_context = {}
        if patient_age > 0:
            patient_context["age"] = patient_age
        if patient_gender:
            patient_context["gender"] = patient_gender
        
        st.session_state.patient_context = patient_context if patient_context else None
    
    def _render_upload_tab(self):
        """Render the upload and analysis tab"""
        st.markdown('<h2 class="sub-header">📄 Upload Medical Report</h2>', unsafe_allow_html=True)
        
        # File upload
        uploaded_file = st.file_uploader(
            "Choose a PDF medical report",
            type=['pdf'],
            help="Upload a medical report in PDF format"
        )
        
        if uploaded_file is not None:
            # Display file info
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("File Name", uploaded_file.name)
            with col2:
                st.metric("File Size", f"{uploaded_file.size / 1024:.1f} KB")
            with col3:
                st.metric("File Type", uploaded_file.type)
            
            # Process button
            if st.button("🔬 Analyze Report", type="primary"):
                if not st.session_state.pipeline_initialized:
                    st.error("❌ Please initialize the pipeline first using the sidebar.")
                    return
                
                # Process the file
                self._process_uploaded_file(uploaded_file)
    
    def _process_uploaded_file(self, uploaded_file):
        """Process the uploaded PDF file"""
        try:
            # Read file content
            pdf_content = uploaded_file.read()
            
            # Initialize pipeline if needed
            if not self.pipeline:
                self.pipeline = MedicalPipeline(
                    use_crewai=st.session_state.get('use_crewai', True),
                    use_langchain=st.session_state.get('use_langchain', True)
                )
            
            # Process with progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Step 1: PDF Processing
            status_text.text("📄 Extracting text from PDF...")
            progress_bar.progress(25)
            time.sleep(0.5)
            
            # Step 2: Entity Extraction
            status_text.text("🔍 Extracting clinical entities...")
            progress_bar.progress(50)
            time.sleep(0.5)
            
            # Step 3: Clinical Analysis
            status_text.text("🧠 Performing clinical analysis...")
            progress_bar.progress(75)
            time.sleep(0.5)
            
            # Step 4: Generate Recommendations
            status_text.text("📋 Generating recommendations...")
            progress_bar.progress(90)
            
            # Actual processing
            result = self.pipeline.process_medical_report(
                pdf_content=pdf_content,
                patient_context=st.session_state.get('patient_context'),
                processing_mode=st.session_state.get('processing_mode', 'comprehensive')
            )
            
            progress_bar.progress(100)
            status_text.text("✅ Analysis complete!")
            
            # Store result
            st.session_state.current_result = result
            st.session_state.processing_results.append({
                "filename": uploaded_file.name,
                "timestamp": time.time(),
                "result": result
            })
            
            # Display results
            self._display_processing_results(result)
            
        except Exception as e:
            st.error(f"❌ Error processing file: {str(e)}")
    
    def _display_processing_results(self, result: ProcessingResult):
        """Display processing results"""
        if result.success:
            st.markdown('<div class="success-box">', unsafe_allow_html=True)
            st.success(f"✅ Analysis completed successfully in {result.processing_time:.2f} seconds")
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Key metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Confidence Score", f"{result.confidence_score:.2%}")
            with col2:
                st.metric("Processing Time", f"{result.processing_time:.1f}s")
            with col3:
                recommendations_count = len(result.recommendations)
                st.metric("Recommendations", recommendations_count)
            with col4:
                abnormalities = result.clinical_interpretation.get('abnormalities', {})
                critical_count = len(abnormalities.get('critical', []))
                st.metric("Critical Findings", critical_count)
            
            # Quick summary
            if 'summary' in result.clinical_interpretation:
                st.subheader("📋 Clinical Summary")
                st.info(result.clinical_interpretation['summary'])
            
            # Critical findings alert
            if abnormalities.get('critical'):
                st.subheader("🚨 Critical Findings")
                for finding in abnormalities['critical']:
                    st.error(f"⚠️ {finding}")
        else:
            st.markdown('<div class="error-box">', unsafe_allow_html=True)
            st.error(f"❌ Analysis failed: {result.error_message}")
            st.markdown('</div>', unsafe_allow_html=True)
    
    def _render_dashboard_tab(self):
        """Render the results dashboard"""
        st.markdown('<h2 class="sub-header">📊 Results Dashboard</h2>', unsafe_allow_html=True)
        
        if not st.session_state.processing_results:
            st.info("📄 No analysis results yet. Upload and analyze a medical report to see results here.")
            return
        
        # Recent results
        st.subheader("📈 Recent Analysis Results")
        
        # Create summary dataframe
        summary_data = []
        for item in st.session_state.processing_results[-10:]:  # Last 10 results
            result = item['result']
            summary_data.append({
                "File": item['filename'],
                "Success": "✅" if result.success else "❌",
                "Confidence": f"{result.confidence_score:.1%}" if result.success else "N/A",
                "Processing Time": f"{result.processing_time:.1f}s",
                "Critical Findings": len(result.clinical_interpretation.get('abnormalities', {}).get('critical', [])) if result.success else 0,
                "Timestamp": time.strftime("%Y-%m-%d %H:%M", time.localtime(item['timestamp']))
            })
        
        if summary_data:
            df = pd.DataFrame(summary_data)
            st.dataframe(df, use_container_width=True)
            
            # Visualizations
            col1, col2 = st.columns(2)
            
            with col1:
                # Confidence score distribution
                if len([r for r in st.session_state.processing_results if r['result'].success]) > 0:
                    confidence_scores = [r['result'].confidence_score for r in st.session_state.processing_results if r['result'].success]
                    fig = px.histogram(x=confidence_scores, title="Confidence Score Distribution", nbins=10)
                    fig.update_xaxis(title="Confidence Score")
                    fig.update_yaxis(title="Count")
                    st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Processing time trend
                processing_times = [r['result'].processing_time for r in st.session_state.processing_results]
                timestamps = [r['timestamp'] for r in st.session_state.processing_results]
                fig = px.line(x=timestamps, y=processing_times, title="Processing Time Trend")
                fig.update_xaxis(title="Time")
                fig.update_yaxis(title="Processing Time (seconds)")
                st.plotly_chart(fig, use_container_width=True)
    
    def _render_analysis_tab(self):
        """Render detailed analysis tab"""
        st.markdown('<h2 class="sub-header">🔍 Detailed Analysis</h2>', unsafe_allow_html=True)
        
        if not st.session_state.current_result:
            st.info("📄 No current analysis. Upload and analyze a medical report to see detailed results here.")
            return
        
        result = st.session_state.current_result
        
        if not result.success:
            st.error(f"❌ Analysis failed: {result.error_message}")
            return
        
        # Tabs for different aspects of analysis
        analysis_tab1, analysis_tab2, analysis_tab3, analysis_tab4 = st.tabs(
            ["📋 Clinical Interpretation", "🧬 Extracted Entities", "💊 Recommendations", "📊 Raw Data"]
        )
        
        with analysis_tab1:
            self._render_clinical_interpretation(result.clinical_interpretation)
        
        with analysis_tab2:
            self._render_extracted_entities(result.clinical_entities)
        
        with analysis_tab3:
            self._render_recommendations(result.recommendations, result.clinical_interpretation)
        
        with analysis_tab4:
            self._render_raw_data(result)
    
    def _render_clinical_interpretation(self, interpretation: Dict[str, Any]):
        """Render clinical interpretation section"""
        
        # Summary
        if 'summary' in interpretation:
            st.subheader("📋 Clinical Summary")
            st.info(interpretation['summary'])
        
        # Abnormalities
        if 'abnormalities' in interpretation:
            abnormalities = interpretation['abnormalities']
            
            if abnormalities.get('critical'):
                st.subheader("🚨 Critical Abnormalities")
                for finding in abnormalities['critical']:
                    st.error(f"⚠️ {finding}")
            
            if abnormalities.get('significant'):
                st.subheader("⚠️ Significant Abnormalities")
                for finding in abnormalities['significant']:
                    st.warning(f"• {finding}")
            
            if abnormalities.get('minor'):
                st.subheader("ℹ️ Minor Abnormalities")
                for finding in abnormalities['minor']:
                    st.info(f"• {finding}")
        
        # Differential diagnoses
        if 'differential_diagnoses' in interpretation:
            st.subheader("🔬 Differential Diagnoses")
            for i, diagnosis in enumerate(interpretation['differential_diagnoses'], 1):
                st.write(f"{i}. {diagnosis}")
        
        # Clinical significance
        if 'clinical_significance' in interpretation:
            st.subheader("📊 Clinical Significance")
            st.write(interpretation['clinical_significance'])
    
    def _render_extracted_entities(self, entities: Dict[str, Any]):
        """Render extracted entities section"""
        
        # Create columns for different entity types
        col1, col2 = st.columns(2)
        
        with col1:
            # Medications
            if 'medications' in entities and entities['medications']:
                st.subheader("💊 Medications")
                for med in entities['medications']:
                    st.write(f"• {med}")
            
            # Conditions
            if 'conditions' in entities and entities['conditions']:
                st.subheader("🏥 Medical Conditions")
                for condition in entities['conditions']:
                    st.write(f"• {condition}")
        
        with col2:
            # Lab results
            if 'lab_results' in entities and entities['lab_results']:
                st.subheader("🧪 Laboratory Results")
                for test, value in entities['lab_results'].items():
                    st.write(f"• {test}: {value}")
            
            # Vital signs
            if 'vital_signs' in entities and entities['vital_signs']:
                st.subheader("❤️ Vital Signs")
                for vital, value in entities['vital_signs'].items():
                    st.write(f"• {vital}: {value}")
    
    def _render_recommendations(self, recommendations: list, interpretation: Dict[str, Any]):
        """Render recommendations section"""
        
        st.subheader("📋 Clinical Recommendations")
        
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                st.write(f"{i}. {rec}")
        else:
            st.info("No specific recommendations generated.")
        
        # Follow-up tests
        if 'follow_up_tests' in interpretation:
            st.subheader("🔬 Recommended Follow-up Tests")
            for test in interpretation['follow_up_tests']:
                st.write(f"• {test}")
    
    def _render_raw_data(self, result: ProcessingResult):
        """Render raw data section"""
        
        st.subheader("📊 Raw Processing Data")
        
        # Convert result to dict for display
        result_dict = {
            "success": result.success,
            "processing_time": result.processing_time,
            "confidence_score": result.confidence_score,
            "pdf_extraction": result.pdf_extraction,
            "clinical_entities": result.clinical_entities,
            "clinical_interpretation": result.clinical_interpretation,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
        
        st.json(result_dict)
    
    def _render_status_tab(self):
        """Render system status tab"""
        st.markdown('<h2 class="sub-header">⚙️ System Status</h2>', unsafe_allow_html=True)
        
        # Pipeline status
        if self.pipeline:
            status = self.pipeline.get_pipeline_status()
            
            st.subheader("🔧 Pipeline Components")
            
            for component, status_info in status.items():
                if isinstance(status_info, dict):
                    st.write(f"**{component.replace('_', ' ').title()}:**")
                    for sub_component, sub_status in status_info.items():
                        color = "🟢" if sub_status == "Ready" else "🔴"
                        st.write(f"  {color} {sub_component}: {sub_status}")
                else:
                    color = "🟢" if status_info == "Ready" else "🔴"
                    st.write(f"{color} **{component.replace('_', ' ').title()}:** {status_info}")
        else:
            st.warning("⚠️ Pipeline not initialized. Please initialize from the sidebar.")
        
        # Configuration info
        st.subheader("⚙️ Configuration")
        config_info = {
            "Model": self.config.GEMINI_MODEL,
            "Temperature": self.config.TEMPERATURE,
            "Max Tokens": self.config.MAX_TOKENS,
            "Max File Size": f"{self.config.MAX_FILE_SIZE_MB} MB"
        }
        
        for key, value in config_info.items():
            st.write(f"**{key}:** {value}")

# Main application
def main():
    app = MedInsightApp()
    app.run()

if __name__ == "__main__":
    main()
