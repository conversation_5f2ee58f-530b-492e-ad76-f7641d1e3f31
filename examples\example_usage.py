"""
Example usage of MedInsight-Agent pipeline
"""
import sys
import json
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.pipeline.medical_pipeline import MedicalPipeline
from src.utils.logging_config import setup_logging

def create_sample_medical_report():
    """Create a sample medical report for testing"""
    sample_report = """
MEDICAL REPORT

Patient Information:
Name: <PERSON>
Age: 45 years
Gender: Male
MRN: 123456789
Date: 2024-01-15

Chief Complaint:
Chest pain and shortness of breath

History of Present Illness:
45-year-old male presents to the emergency department with acute onset of chest pain 
that started approximately 2 hours ago. Patient describes the pain as crushing, 
substernal, radiating to the left arm and jaw. Associated with diaphoresis and 
shortness of breath. Pain is 8/10 in severity. No alleviating factors identified.

Past Medical History:
- Hypertension (diagnosed 2020)
- Hyperlipidemia (diagnosed 2019)
- Type 2 Diabetes Mellitus (diagnosed 2018)
- Family history of coronary artery disease

Current Medications:
- Lisinopril 10mg daily
- Atorvastatin 40mg daily
- Metformin 1000mg twice daily
- Aspirin 81mg daily

Physical Examination:
Vital Signs:
- Blood Pressure: 150/95 mmHg
- Heart Rate: 88 bpm
- Respiratory Rate: 20/min
- Temperature: 98.6°F (37°C)
- Oxygen Saturation: 96% on room air

General: Alert, oriented, appears uncomfortable, diaphoretic
Cardiovascular: Regular rate and rhythm, no murmurs, rubs, or gallops
Pulmonary: Clear to auscultation bilaterally
Abdomen: Soft, non-tender, non-distended

Laboratory Results:
- Troponin I: 0.8 ng/mL (elevated, normal <0.04)
- CK-MB: 15 ng/mL (elevated, normal <6.3)
- Total Cholesterol: 245 mg/dL (high, normal <200)
- LDL: 165 mg/dL (high, normal <100)
- HDL: 35 mg/dL (low, normal >40 for men)
- Triglycerides: 220 mg/dL (high, normal <150)
- Glucose: 180 mg/dL (elevated, normal 70-100)
- HbA1c: 8.2% (poor control, target <7%)
- Creatinine: 1.2 mg/dL (normal 0.7-1.3)
- BUN: 22 mg/dL (normal 7-20)

Diagnostic Studies:
ECG: ST-elevation in leads II, III, aVF consistent with inferior STEMI
Chest X-ray: No acute cardiopulmonary process

Assessment and Plan:
1. ST-Elevation Myocardial Infarction (STEMI) - Inferior wall
   - Emergent cardiac catheterization
   - Dual antiplatelet therapy (aspirin + clopidogrel)
   - Anticoagulation with heparin
   - Beta-blocker when hemodynamically stable
   - ACE inhibitor
   - Statin therapy

2. Diabetes Mellitus Type 2 - poorly controlled
   - Continue metformin
   - Consider insulin therapy
   - Diabetes education
   - Endocrinology consultation

3. Hypertension
   - Continue ACE inhibitor
   - Monitor blood pressure
   - Lifestyle modifications

4. Hyperlipidemia
   - High-intensity statin therapy
   - Lifestyle modifications
   - Follow-up lipid panel in 6-8 weeks

Disposition:
Patient admitted to cardiac intensive care unit for further management.

Dr. Sarah Johnson, MD
Emergency Medicine
"""
    return sample_report

def example_basic_usage():
    """Example of basic pipeline usage"""
    print("=== Basic Pipeline Usage Example ===")
    
    # Setup logging
    setup_logging()
    
    # Create pipeline (without CrewAI and LangChain for faster processing)
    pipeline = MedicalPipeline(use_crewai=False, use_langchain=False)
    
    # Create sample medical report
    sample_text = create_sample_medical_report()
    
    # Process the text directly (simulating PDF extraction)
    print("Processing sample medical report...")
    
    # For this example, we'll simulate PDF content by encoding the text
    pdf_content = sample_text.encode('utf-8')
    
    # Process through pipeline
    result = pipeline.process_medical_report(
        pdf_content=pdf_content,
        patient_context={"age": 45, "gender": "Male"},
        processing_mode="fast"
    )
    
    # Display results
    if result.success:
        print(f"✅ Processing successful! (Time: {result.processing_time:.2f}s)")
        print(f"📊 Confidence Score: {result.confidence_score:.2%}")
        
        # Clinical interpretation
        if 'summary' in result.clinical_interpretation:
            print(f"\n📋 Clinical Summary:")
            print(result.clinical_interpretation['summary'])
        
        # Abnormalities
        abnormalities = result.clinical_interpretation.get('abnormalities', {})
        if abnormalities.get('critical'):
            print(f"\n🚨 Critical Findings:")
            for finding in abnormalities['critical']:
                print(f"  ⚠️ {finding}")
        
        # Recommendations
        print(f"\n💊 Recommendations:")
        for i, rec in enumerate(result.recommendations, 1):
            print(f"  {i}. {rec}")
        
    else:
        print(f"❌ Processing failed: {result.error_message}")

def example_comprehensive_usage():
    """Example of comprehensive pipeline usage with all features"""
    print("\n=== Comprehensive Pipeline Usage Example ===")
    
    # Create pipeline with all features enabled
    pipeline = MedicalPipeline(use_crewai=True, use_langchain=True)
    
    # Create sample medical report
    sample_text = create_sample_medical_report()
    pdf_content = sample_text.encode('utf-8')
    
    # Enhanced patient context
    patient_context = {
        "age": 45,
        "gender": "Male",
        "medical_history": ["Hypertension", "Hyperlipidemia", "Type 2 Diabetes"],
        "allergies": ["NKDA"],
        "emergency_contact": "Spouse"
    }
    
    print("Processing with comprehensive analysis...")
    
    # Process through pipeline
    result = pipeline.process_medical_report(
        pdf_content=pdf_content,
        patient_context=patient_context,
        processing_mode="comprehensive"
    )
    
    # Display detailed results
    if result.success:
        print(f"✅ Comprehensive analysis complete! (Time: {result.processing_time:.2f}s)")
        
        # Detailed clinical interpretation
        interpretation = result.clinical_interpretation
        
        if 'interpretation' in interpretation:
            print(f"\n🔬 Detailed Clinical Interpretation:")
            print(interpretation['interpretation'])
        
        # Differential diagnoses
        if 'differential_diagnoses' in interpretation:
            print(f"\n🩺 Differential Diagnoses:")
            for i, diagnosis in enumerate(interpretation['differential_diagnoses'], 1):
                print(f"  {i}. {diagnosis}")
        
        # Follow-up tests
        if 'follow_up_tests' in interpretation:
            print(f"\n🧪 Recommended Follow-up Tests:")
            for test in interpretation['follow_up_tests']:
                print(f"  • {test}")
        
        # Clinical significance
        if 'clinical_significance' in interpretation:
            print(f"\n📊 Clinical Significance:")
            print(interpretation['clinical_significance'])
        
    else:
        print(f"❌ Comprehensive analysis failed: {result.error_message}")

def example_entity_extraction():
    """Example of clinical entity extraction"""
    print("\n=== Clinical Entity Extraction Example ===")
    
    from src.tools.clinical_nlp import ClinicalNLPProcessor
    
    # Initialize NLP processor
    nlp_processor = ClinicalNLPProcessor()
    
    # Sample medical text
    sample_text = create_sample_medical_report()
    
    print("Extracting clinical entities...")
    
    # Extract structured data
    structured_data = nlp_processor.extract_structured_data(sample_text)
    
    # Display extracted entities
    print(f"\n👤 Patient Information:")
    patient_info = structured_data.get('patient_info', {})
    for key, value in patient_info.items():
        print(f"  {key.title()}: {value}")
    
    print(f"\n💊 Medications:")
    medications = structured_data.get('medications', [])
    for med in medications:
        print(f"  • {med}")
    
    print(f"\n🏥 Medical Conditions:")
    conditions = structured_data.get('conditions', [])
    for condition in conditions:
        print(f"  • {condition}")
    
    print(f"\n🧪 Laboratory Results:")
    lab_results = structured_data.get('lab_results', {})
    for test, value in lab_results.items():
        print(f"  • {test}: {value}")
    
    print(f"\n❤️ Vital Signs:")
    vital_signs = structured_data.get('vital_signs', {})
    for vital, value in vital_signs.items():
        print(f"  • {vital}: {value}")

def example_save_results():
    """Example of saving processing results"""
    print("\n=== Saving Results Example ===")
    
    # Create pipeline
    pipeline = MedicalPipeline(use_crewai=False, use_langchain=False)
    
    # Process sample report
    sample_text = create_sample_medical_report()
    pdf_content = sample_text.encode('utf-8')
    
    result = pipeline.process_medical_report(pdf_content)
    
    if result.success:
        # Convert result to dictionary for JSON serialization
        result_dict = {
            "success": result.success,
            "processing_time": result.processing_time,
            "confidence_score": result.confidence_score,
            "clinical_interpretation": result.clinical_interpretation,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
        
        # Save to file
        output_file = Path("sample_analysis_result.json")
        with open(output_file, 'w') as f:
            json.dump(result_dict, f, indent=2, default=str)
        
        print(f"✅ Results saved to {output_file}")
        print(f"📊 File size: {output_file.stat().st_size} bytes")
    
    else:
        print(f"❌ Cannot save results: {result.error_message}")

def main():
    """Main example runner"""
    print("🏥 MedInsight-Agent Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_usage()
        example_entity_extraction()
        example_save_results()
        
        # Note: Comprehensive example requires API keys
        print("\n📝 Note: To run the comprehensive example with CrewAI and LangChain,")
        print("   please ensure you have set up your Google API key in the .env file.")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("💡 Make sure you have installed all dependencies and set up your environment.")

if __name__ == "__main__":
    main()
