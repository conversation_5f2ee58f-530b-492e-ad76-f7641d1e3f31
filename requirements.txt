# Core AI and ML frameworks
crewai==0.70.1
langchain==0.1.20
langchain-community==0.0.38
langchain-google-genai==1.0.10
google-generativeai==0.8.3

# PDF processing (install these for PDF functionality)
# PyMuPDF==1.24.10      # Uncomment to enable PDF processing
# pdfminer.six==20231228 # Uncomment to enable PDF processing

# Clinical NLP and medical processing
spacy==3.7.6
scispacy==0.5.4
medspacy==1.0.0
transformers==4.45.2
torch==2.1.2

# Data processing and utilities
pandas==2.2.2
numpy==1.26.4
requests==2.32.3
python-dotenv==1.0.1
pydantic==2.9.2

# Web interface
streamlit==1.39.0
streamlit-option-menu==0.3.13
plotly==5.17.0

# Database and storage (optional)
# chromadb==0.5.15  # Commented out - requires C++ build tools on Windows
# faiss-cpu==1.8.0  # Commented out - not needed without chromadb
