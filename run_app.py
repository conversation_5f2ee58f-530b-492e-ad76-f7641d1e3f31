#!/usr/bin/env python3
"""
Main entry point for MedInsight-Agent application
"""
import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import streamlit
        import crewai
        import langchain
        import google.generativeai
        print("✅ All core dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Please run: pip install -r requirements.txt")
        return False

def check_environment():
    """Check environment configuration"""
    from config import Config
    
    try:
        config = Config()
        if not config.GOOGLE_API_KEY:
            print("❌ GOOGLE_API_KEY not found in environment")
            print("💡 Please set up your .env file with your Google API key")
            return False
        
        print("✅ Environment configuration valid")
        return True
    except Exception as e:
        print(f"❌ Environment check failed: {e}")
        return False

def setup_logging():
    """Setup application logging"""
    try:
        from src.utils.logging_config import setup_logging
        setup_logging()
        print("✅ Logging configured")
        return True
    except Exception as e:
        print(f"❌ Logging setup failed: {e}")
        return False

def run_streamlit_app():
    """Run the Streamlit application"""
    try:
        print("🚀 Starting MedInsight-Agent...")
        print("📱 Opening Streamlit app in your browser...")
        
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.headless", "false",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")

def run_example():
    """Run example usage"""
    try:
        print("🧪 Running example usage...")
        subprocess.run([sys.executable, "examples/example_usage.py"])
    except Exception as e:
        print(f"❌ Error running example: {e}")

def run_tests():
    """Run test suite"""
    try:
        print("🧪 Running test suite...")
        subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"])
    except Exception as e:
        print(f"❌ Error running tests: {e}")

def main():
    """Main entry point"""
    print("🏥 MedInsight-Agent")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup logging
    if not setup_logging():
        print("⚠️ Continuing without proper logging setup")
    
    # Check environment
    if not check_environment():
        print("⚠️ Environment issues detected - some features may not work")
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "app":
            run_streamlit_app()
        elif command == "example":
            run_example()
        elif command == "test":
            run_tests()
        elif command == "help":
            print_help()
        else:
            print(f"❌ Unknown command: {command}")
            print_help()
    else:
        # Default: run the Streamlit app
        run_streamlit_app()

def print_help():
    """Print help information"""
    print("""
Usage: python run_app.py [command]

Commands:
  app      Run the Streamlit web application (default)
  example  Run example usage scripts
  test     Run the test suite
  help     Show this help message

Examples:
  python run_app.py           # Run web app
  python run_app.py app       # Run web app
  python run_app.py example   # Run examples
  python run_app.py test      # Run tests

For more information, see README.md
""")

if __name__ == "__main__":
    main()
