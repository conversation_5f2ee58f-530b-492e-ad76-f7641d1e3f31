"""
CrewAI agents for medical report processing
"""
import logging
from typing import Dict, List, Optional, Any, Union

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from langchain_google_genai import ChatGoogleGenerativeAI

from config import Config
from src.tools.pdf_processor import PDFProcessor
from src.tools.clinical_nlp import ClinicalNLPProcessor

from src.models.gemini_client import GeminiClinicalClient

logger = logging.getLogger(__name__)

class PDFProcessingTool(BaseTool):
    """Tool for PDF processing and OCR"""
    name: str = "pdf_processor"
    description: str = "Process PDF medical reports and extract text using OCR when needed"
    
    def __init__(self):
        super().__init__()
        self.processor = PDFProcessor()
    
    def _run(self, pdf_content: Union[bytes, str]) -> Dict[str, Any]:
        """Process PDF and extract text"""
        return self.processor.process_medical_pdf(pdf_content)

class ClinicalNLPTool(BaseTool):
    """Tool for clinical NLP and entity extraction"""
    name: str = "clinical_nlp"
    description: str = "Extract clinical entities and structured data from medical text"
    
    def __init__(self):
        super().__init__()
        self.processor = ClinicalNLPProcessor()
    
    def _run(self, text: str) -> Dict[str, Any]:
        """Extract clinical entities from text"""
        return self.processor.extract_structured_data(text)

class KnowledgeBaseTool(BaseTool):
    """Tool for local knowledge base retrieval"""
    name: str = "knowledge_retriever"
    description: str = "Retrieve relevant clinical information from local knowledge base"

    def __init__(self):
        super().__init__()

    def _run(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """Search local knowledge base for relevant information"""
        # Simple mock implementation - in practice, this would search a local database
        return [
            {
                "title": f"Clinical Guidelines for {query}",
                "content": f"Standard clinical guidelines and best practices for {query}",
                "source": "Local Knowledge Base",
                "relevance_score": 0.8
            }
        ]

class ClinicalInterpretationTool(BaseTool):
    """Tool for clinical interpretation using Gemini"""
    name: str = "clinical_interpreter"
    description: str = "Provide clinical interpretation and insights using AI"
    
    def __init__(self):
        super().__init__()
        self.client = GeminiClinicalClient()
    
    def _run(self, clinical_data: Dict[str, Any], context: Optional[str] = None) -> Dict[str, Any]:
        """Generate clinical interpretation"""
        interpretation = self.client.interpret_clinical_data(clinical_data, context)
        return {
            "summary": interpretation.summary,
            "abnormalities": interpretation.abnormalities,
            "interpretation": interpretation.interpretation,
            "recommendations": interpretation.recommendations,
            "differential_diagnoses": interpretation.differential_diagnoses,
            "follow_up_tests": interpretation.follow_up_tests,
            "clinical_significance": interpretation.clinical_significance,
            "confidence_score": interpretation.confidence_score,
            "citations": interpretation.citations
        }

class MedicalAgentCrew:
    """
    CrewAI-based medical processing crew with specialized agents
    """
    
    def __init__(self):
        self.config = Config()
        self._setup_llm()
        self._setup_tools()
        self._setup_agents()
        self._setup_crew()
        
    def _setup_llm(self):
        """Initialize the LLM for agents"""
        self.llm = ChatGoogleGenerativeAI(
            model=self.config.GEMINI_MODEL,
            google_api_key=self.config.GOOGLE_API_KEY,
            temperature=self.config.TEMPERATURE
        )
        
    def _setup_tools(self):
        """Initialize tools for agents"""
        self.pdf_tool = PDFProcessingTool()
        self.nlp_tool = ClinicalNLPTool()
        self.knowledge_tool = KnowledgeBaseTool()
        self.interpretation_tool = ClinicalInterpretationTool()
        
    def _setup_agents(self):
        """Setup specialized medical agents"""
        
        # PDF Ingestion Agent
        self.pdf_agent = Agent(
            role="Medical Document Processor",
            goal="Extract and clean text from medical PDF reports with high accuracy",
            backstory="""You are a specialized medical document processing expert with extensive 
            experience in handling various types of medical reports, lab results, and clinical documents. 
            You excel at extracting text from both digital and scanned PDFs, ensuring no critical 
            medical information is lost in the process.""",
            tools=[self.pdf_tool],
            llm=self.llm,
            verbose=True,
            allow_delegation=False,
            max_iter=3
        )
        
        # Clinical Entity Extraction Agent
        self.extraction_agent = Agent(
            role="Clinical Data Extraction Specialist",
            goal="Extract and structure clinical entities from medical text with precision",
            backstory="""You are a clinical informatics expert specializing in medical natural 
            language processing. You have deep knowledge of medical terminology, clinical workflows, 
            and healthcare data standards. You excel at identifying and extracting key clinical 
            entities such as medications, conditions, lab values, and vital signs from unstructured 
            medical text.""",
            tools=[self.nlp_tool],
            llm=self.llm,
            verbose=True,
            allow_delegation=False,
            max_iter=3
        )
        
        # Knowledge Research Agent
        self.research_agent = Agent(
            role="Medical Knowledge Research Specialist",
            goal="Find relevant clinical guidelines and evidence-based information",
            backstory="""You are a medical knowledge specialist with expertise in
            evidence-based medicine. You excel at finding the most relevant clinical
            guidelines, best practices, and medical knowledge from available resources.
            You understand how to formulate effective search queries and evaluate the quality
            and relevance of medical information.""",
            tools=[self.knowledge_tool],
            llm=self.llm,
            verbose=True,
            allow_delegation=False,
            max_iter=3
        )
        
        # Clinical Interpretation Agent
        self.interpretation_agent = Agent(
            role="Clinical Interpretation Expert",
            goal="Provide comprehensive clinical analysis and actionable insights",
            backstory="""You are a senior clinician with extensive experience in internal medicine, 
            diagnostics, and clinical decision-making. You excel at interpreting complex clinical 
            data, identifying patterns and abnormalities, formulating differential diagnoses, and 
            providing evidence-based recommendations. You always prioritize patient safety and 
            follow current clinical guidelines.""",
            tools=[self.interpretation_tool],
            llm=self.llm,
            verbose=True,
            allow_delegation=False,
            max_iter=3
        )
        
    def _setup_crew(self):
        """Setup the CrewAI crew with tasks and process"""
        # This will be defined when processing specific documents
        self.crew = None
        
    def process_medical_report(self, pdf_content: Union[bytes, str], patient_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Process a medical report through the complete pipeline
        
        Args:
            pdf_content: PDF file content (bytes or file path)
            patient_context: Optional patient context information
            
        Returns:
            Complete analysis results
        """
        try:
            # Define tasks for this specific processing job
            tasks = self._create_processing_tasks(pdf_content, patient_context)
            
            # Create crew for this job
            crew = Crew(
                agents=[
                    self.pdf_agent,
                    self.extraction_agent,
                    self.research_agent,
                    self.interpretation_agent
                ],
                tasks=tasks,
                process=Process.sequential,
                verbose=True
            )
            
            # Execute the crew
            result = crew.kickoff()
            
            return {
                "success": True,
                "result": result,
                "crew_output": crew.usage_metrics if hasattr(crew, 'usage_metrics') else None
            }
            
        except Exception as e:
            logger.error(f"Error processing medical report: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_processing_tasks(self, pdf_content: Union[bytes, str], patient_context: Optional[Dict] = None) -> List[Task]:
        """Create tasks for medical report processing"""
        
        # Task 1: PDF Processing
        pdf_task = Task(
            description=f"""
            Process the provided medical PDF report and extract clean, readable text.
            
            PDF Content: {pdf_content if isinstance(pdf_content, str) else "Binary PDF data provided"}
            
            Requirements:
            1. Extract all text content from the PDF
            2. Use OCR if the PDF contains scanned images
            3. Clean and format the extracted text
            4. Preserve medical terminology and formatting
            5. Identify any extraction issues or limitations
            
            Output: Clean, structured text ready for clinical analysis
            """,
            agent=self.pdf_agent,
            expected_output="Extracted and cleaned medical report text with metadata about the extraction process"
        )
        
        # Task 2: Clinical Entity Extraction
        extraction_task = Task(
            description="""
            Extract and structure clinical entities from the processed medical text.
            
            Use the extracted text from the previous task to identify:
            1. Patient demographics and identifiers
            2. Chief complaint and history of present illness
            3. Medications and dosages
            4. Medical conditions and diagnoses
            5. Laboratory values and results
            6. Vital signs and measurements
            7. Physical examination findings
            8. Assessment and plan information
            
            Output: Structured JSON containing all extracted clinical entities
            """,
            agent=self.extraction_agent,
            expected_output="Structured clinical data in JSON format with all relevant medical entities extracted and categorized"
        )
        
        # Task 3: Knowledge Research
        research_task = Task(
            description="""
            Research relevant clinical guidelines and evidence-based information for the identified conditions and findings.

            Based on the extracted clinical entities:
            1. Identify the primary medical conditions mentioned
            2. Search for current clinical guidelines for these conditions
            3. Find relevant diagnostic criteria and treatment recommendations
            4. Look for standard management approaches
            5. Prioritize established clinical guidelines and best practices

            Output: Relevant clinical guidelines and medical knowledge
            """,
            agent=self.research_agent,
            expected_output="Curated list of relevant clinical guidelines and medical knowledge with relevance scores"
        )
        
        # Task 4: Clinical Interpretation
        interpretation_task = Task(
            description=f"""
            Provide comprehensive clinical interpretation and actionable insights.
            
            Patient Context: {patient_context or "No additional context provided"}
            
            Using the extracted clinical data and relevant literature:
            1. Identify abnormal findings and their clinical significance
            2. Provide differential diagnoses with reasoning
            3. Assess the overall clinical picture and risk factors
            4. Generate evidence-based recommendations for management
            5. Suggest appropriate follow-up care and monitoring
            6. Highlight any critical findings requiring immediate attention
            7. Provide patient education points if applicable
            
            Output: Comprehensive clinical interpretation with structured recommendations
            """,
            agent=self.interpretation_agent,
            expected_output="Complete clinical interpretation with summary, abnormalities, differential diagnoses, recommendations, and follow-up plan in structured format"
        )
        
        return [pdf_task, extraction_task, research_task, interpretation_task]
    
    def get_agent_status(self) -> Dict[str, str]:
        """Get status of all agents"""
        return {
            "pdf_agent": "Ready" if self.pdf_agent else "Not initialized",
            "extraction_agent": "Ready" if self.extraction_agent else "Not initialized",
            "research_agent": "Ready" if self.research_agent else "Not initialized",
            "interpretation_agent": "Ready" if self.interpretation_agent else "Not initialized"
        }
