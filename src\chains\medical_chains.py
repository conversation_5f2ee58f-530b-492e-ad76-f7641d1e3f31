"""
LangChain chains for medical data processing with memory and RAG capabilities
"""
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from langchain.chains import <PERSON><PERSON>hain
from langchain.memory import ConversationBufferWindowMemory, ConversationSummaryMemory
from langchain.schema import BaseMemory
from langchain.prompts import PromptTemplate
from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from langchain.vectorstores import Chroma
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from config import Config
from src.tools.pubmed_retrieval import PubMedRetriever, PubMedArticle

logger = logging.getLogger(__name__)

class MedicalProcessingChains:
    """
    LangChain-based medical processing chains with memory and RAG
    """
    
    def __init__(self):
        self.config = Config()
        self._setup_llm()
        self._setup_memory()
        self._setup_embeddings()
        self._setup_vector_store()
        self._setup_chains()
        self.pubmed_retriever = PubMedRetriever()
        
    def _setup_llm(self):
        """Initialize the LLM"""
        self.llm = ChatGoogleGenerativeAI(
            model=self.config.GEMINI_MODEL,
            google_api_key=self.config.GOOGLE_API_KEY,
            temperature=self.config.TEMPERATURE,
            max_output_tokens=self.config.MAX_TOKENS
        )
        logger.info("LangChain LLM initialized")
    
    def _setup_memory(self):
        """Setup conversation memory"""
        self.memory = ConversationBufferWindowMemory(
            k=5,  # Keep last 5 interactions
            memory_key="chat_history",
            return_messages=True
        )
        
        self.summary_memory = ConversationSummaryMemory(
            llm=self.llm,
            memory_key="summary",
            return_messages=True
        )
        
    def _setup_embeddings(self):
        """Setup embeddings for RAG"""
        self.embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2"
        )
        
    def _setup_vector_store(self):
        """Setup vector store for RAG"""
        persist_directory = self.config.CHROMA_PERSIST_DIRECTORY
        
        self.vector_store = Chroma(
            persist_directory=str(persist_directory),
            embedding_function=self.embeddings,
            collection_name="medical_knowledge"
        )
        
    def _setup_chains(self):
        """Setup LangChain chains"""
        
        # PDF Processing Chain
        pdf_prompt = PromptTemplate(
            input_variables=["pdf_text", "chat_history"],
            template="""
You are a medical AI assistant processing a medical report. 

Previous context: {chat_history}

Medical Report Text:
{pdf_text}

Please extract and structure the key medical information from this report. Focus on:
1. Patient demographics
2. Chief complaint and history
3. Physical examination findings
4. Laboratory and diagnostic results
5. Assessment and plan
6. Medications and treatments

Provide a structured summary that will be used for further clinical analysis.
"""
        )
        
        self.pdf_chain = LLMChain(
            llm=self.llm,
            prompt=pdf_prompt,
            memory=self.memory,
            verbose=True
        )
        
        # Clinical Analysis Chain
        analysis_prompt = PromptTemplate(
            input_variables=["clinical_data", "guidelines", "chat_history"],
            template="""
You are an expert clinical AI providing medical interpretation.

Previous context: {chat_history}

Clinical Data:
{clinical_data}

Relevant Clinical Guidelines:
{guidelines}

Provide a comprehensive clinical analysis including:
1. Identification of abnormal findings
2. Clinical significance and interpretation
3. Differential diagnoses with reasoning
4. Evidence-based recommendations
5. Follow-up care and monitoring
6. Risk stratification

Base your analysis on current clinical guidelines and evidence-based medicine.
"""
        )
        
        self.analysis_chain = LLMChain(
            llm=self.llm,
            prompt=analysis_prompt,
            memory=self.memory,
            verbose=True
        )
        
        # Recommendation Chain
        recommendation_prompt = PromptTemplate(
            input_variables=["analysis", "patient_context", "guidelines", "chat_history"],
            template="""
You are a clinical decision support AI providing actionable recommendations.

Previous context: {chat_history}

Clinical Analysis:
{analysis}

Patient Context:
{patient_context}

Clinical Guidelines:
{guidelines}

Provide specific, actionable recommendations including:
1. Immediate actions required
2. Treatment modifications
3. Additional testing or imaging
4. Specialist referrals
5. Patient education points
6. Follow-up timeline
7. Monitoring parameters

Prioritize recommendations by urgency and clinical importance.
"""
        )
        
        self.recommendation_chain = LLMChain(
            llm=self.llm,
            prompt=recommendation_prompt,
            memory=self.memory,
            verbose=True
        )
        
    def process_pdf_content(self, pdf_text: str, patient_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process PDF content through LangChain
        
        Args:
            pdf_text: Extracted PDF text
            patient_id: Optional patient identifier for context
            
        Returns:
            Processed medical information
        """
        try:
            # Add to vector store for future RAG
            self._add_to_knowledge_base(pdf_text, patient_id)
            
            # Process through chain
            result = self.pdf_chain.run(pdf_text=pdf_text)
            
            return {
                "success": True,
                "processed_content": result,
                "patient_id": patient_id
            }
            
        except Exception as e:
            logger.error(f"Error processing PDF content: {e}")
            return {"success": False, "error": str(e)}
    
    def analyze_clinical_data(self, 
                            clinical_data: Dict[str, Any],
                            retrieve_guidelines: bool = True) -> Dict[str, Any]:
        """
        Analyze clinical data with RAG-enhanced guidelines
        
        Args:
            clinical_data: Structured clinical data
            retrieve_guidelines: Whether to retrieve relevant guidelines
            
        Returns:
            Clinical analysis results
        """
        try:
            guidelines = ""
            
            if retrieve_guidelines:
                # Extract conditions for guideline retrieval
                conditions = clinical_data.get("conditions", [])
                if conditions:
                    guidelines = self._retrieve_clinical_guidelines(conditions)
            
            # Run analysis chain
            result = self.analysis_chain.run(
                clinical_data=str(clinical_data),
                guidelines=guidelines
            )
            
            return {
                "success": True,
                "analysis": result,
                "guidelines_used": bool(guidelines)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing clinical data: {e}")
            return {"success": False, "error": str(e)}
    
    def generate_recommendations(self, 
                               analysis: str,
                               patient_context: Dict[str, Any],
                               retrieve_guidelines: bool = True) -> Dict[str, Any]:
        """
        Generate clinical recommendations
        
        Args:
            analysis: Clinical analysis text
            patient_context: Patient demographic and clinical context
            retrieve_guidelines: Whether to retrieve relevant guidelines
            
        Returns:
            Clinical recommendations
        """
        try:
            guidelines = ""
            
            if retrieve_guidelines:
                # Extract relevant terms for guideline retrieval
                conditions = patient_context.get("conditions", [])
                if conditions:
                    guidelines = self._retrieve_clinical_guidelines(conditions)
            
            # Run recommendation chain
            result = self.recommendation_chain.run(
                analysis=analysis,
                patient_context=str(patient_context),
                guidelines=guidelines
            )
            
            return {
                "success": True,
                "recommendations": result,
                "guidelines_used": bool(guidelines)
            }
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return {"success": False, "error": str(e)}
    
    def _add_to_knowledge_base(self, text: str, patient_id: Optional[str] = None):
        """Add document to vector store for RAG"""
        try:
            # Split text into chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200
            )
            
            chunks = text_splitter.split_text(text)
            
            # Create documents
            documents = []
            for i, chunk in enumerate(chunks):
                metadata = {
                    "chunk_id": i,
                    "source": "medical_report"
                }
                if patient_id:
                    metadata["patient_id"] = patient_id
                    
                documents.append(Document(page_content=chunk, metadata=metadata))
            
            # Add to vector store
            self.vector_store.add_documents(documents)
            self.vector_store.persist()
            
            logger.info(f"Added {len(documents)} chunks to knowledge base")
            
        except Exception as e:
            logger.error(f"Error adding to knowledge base: {e}")
    
    def _retrieve_clinical_guidelines(self, conditions: List[str]) -> str:
        """Retrieve relevant clinical guidelines"""
        try:
            all_guidelines = []
            
            for condition in conditions[:3]:  # Limit to top 3 conditions
                articles = self.pubmed_retriever.get_clinical_guidelines(
                    condition=condition,
                    max_results=2
                )
                
                for article in articles:
                    guideline_text = f"""
Title: {article.title}
Journal: {article.journal}
Abstract: {article.abstract[:500]}...
PMID: {article.pmid}
"""
                    all_guidelines.append(guideline_text)
            
            return "\n\n".join(all_guidelines)
            
        except Exception as e:
            logger.error(f"Error retrieving guidelines: {e}")
            return ""
    
    def search_knowledge_base(self, query: str, k: int = 5) -> List[Document]:
        """Search the knowledge base for relevant information"""
        try:
            results = self.vector_store.similarity_search(query, k=k)
            return results
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    def get_patient_history(self, patient_id: str) -> List[Document]:
        """Retrieve patient history from knowledge base"""
        try:
            # Search for documents with specific patient ID
            results = self.vector_store.similarity_search(
                query="",
                k=20,
                filter={"patient_id": patient_id}
            )
            return results
            
        except Exception as e:
            logger.error(f"Error retrieving patient history: {e}")
            return []
    
    def clear_memory(self):
        """Clear conversation memory"""
        self.memory.clear()
        self.summary_memory.clear()
        logger.info("Memory cleared")
    
    def get_memory_summary(self) -> str:
        """Get summary of conversation memory"""
        try:
            return self.summary_memory.buffer
        except:
            return "No conversation history available"
