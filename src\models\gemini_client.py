"""
Gemini-2.5-Flash model client for clinical interpretation
"""
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ClinicalInterpretation:
    """Data class for clinical interpretation results"""
    summary: str
    abnormalities: Dict[str, Any]
    interpretation: str
    recommendations: List[str]
    differential_diagnoses: List[str]
    follow_up_tests: List[str]
    clinical_significance: str
    confidence_score: float
    citations: List[str] = None

class GeminiClinicalClient:
    """
    Gemini-2.5-Flash client for clinical interpretation and analysis
    """
    
    def __init__(self):
        self.config = Config()
        self._setup_client()
        
    def _setup_client(self):
        """Initialize Gemini client"""
        try:
            genai.configure(api_key=self.config.GOOGLE_API_KEY)
            
            # Configure safety settings for medical content
            self.safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
            
            # Initialize model
            self.model = genai.GenerativeModel(
                model_name=self.config.GEMINI_MODEL,
                safety_settings=self.safety_settings
            )
            
            logger.info(f"Initialized Gemini model: {self.config.GEMINI_MODEL}")
            
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            raise
    
    def interpret_clinical_data(self, 
                              clinical_data: Dict[str, Any],
                              context: Optional[str] = None) -> ClinicalInterpretation:
        """
        Interpret clinical data and provide medical insights
        
        Args:
            clinical_data: Structured clinical data from NLP extraction
            context: Additional context or previous reports
            
        Returns:
            ClinicalInterpretation object with analysis results
        """
        try:
            # Build comprehensive prompt
            prompt = self._build_interpretation_prompt(clinical_data, context)
            
            # Generate interpretation
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.config.TEMPERATURE,
                    max_output_tokens=self.config.MAX_TOKENS,
                    response_mime_type="application/json"
                )
            )
            
            # Parse response
            interpretation = self._parse_interpretation_response(response.text)
            
            logger.info("Clinical interpretation completed successfully")
            return interpretation
            
        except Exception as e:
            logger.error(f"Error in clinical interpretation: {e}")
            # Return fallback interpretation
            return self._create_fallback_interpretation(clinical_data)
    
    def _build_interpretation_prompt(self, 
                                   clinical_data: Dict[str, Any], 
                                   context: Optional[str] = None) -> str:
        """Build comprehensive prompt for clinical interpretation"""
        
        prompt = f"""
You are an expert clinical AI assistant analyzing medical report data. Provide a comprehensive clinical interpretation of the following medical data.

CLINICAL DATA:
{json.dumps(clinical_data, indent=2)}

{f"ADDITIONAL CONTEXT: {context}" if context else ""}

Please provide a detailed clinical interpretation in the following JSON format:

{{
    "summary": "Brief 2-3 sentence summary of the key findings",
    "abnormalities": {{
        "critical": ["List of critical abnormal findings requiring immediate attention"],
        "significant": ["List of significant abnormal findings"],
        "minor": ["List of minor abnormal findings or borderline values"]
    }},
    "interpretation": "Detailed clinical interpretation of findings, including pathophysiology and clinical significance",
    "differential_diagnoses": [
        "Most likely diagnosis based on findings",
        "Alternative diagnosis 1 with reasoning",
        "Alternative diagnosis 2 with reasoning"
    ],
    "recommendations": [
        "Immediate actions or treatments needed",
        "Follow-up care recommendations",
        "Lifestyle or medication adjustments"
    ],
    "follow_up_tests": [
        "Additional tests or imaging recommended",
        "Monitoring parameters to track",
        "Specialist referrals if needed"
    ],
    "clinical_significance": "Overall assessment of the clinical significance and urgency",
    "confidence_score": 0.85,
    "citations": [
        "Relevant clinical guidelines or evidence-based recommendations"
    ]
}}

IMPORTANT GUIDELINES:
1. Focus on clinically relevant findings and their implications
2. Identify abnormal values and explain their significance
3. Consider differential diagnoses based on the constellation of findings
4. Provide evidence-based recommendations following current clinical guidelines
5. Assess urgency and prioritize critical findings
6. Be specific about follow-up care and monitoring
7. Include confidence assessment for your interpretation
8. Reference relevant clinical guidelines when possible
9. Consider patient safety and appropriate level of care
10. Maintain professional medical terminology while being clear

Ensure your response is valid JSON format and comprehensive yet concise.
"""
        
        return prompt
    
    def _parse_interpretation_response(self, response_text: str) -> ClinicalInterpretation:
        """Parse Gemini response into ClinicalInterpretation object"""
        try:
            # Clean response text
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]
            
            # Parse JSON
            data = json.loads(response_text)
            
            return ClinicalInterpretation(
                summary=data.get("summary", ""),
                abnormalities=data.get("abnormalities", {}),
                interpretation=data.get("interpretation", ""),
                recommendations=data.get("recommendations", []),
                differential_diagnoses=data.get("differential_diagnoses", []),
                follow_up_tests=data.get("follow_up_tests", []),
                clinical_significance=data.get("clinical_significance", ""),
                confidence_score=data.get("confidence_score", 0.0),
                citations=data.get("citations", [])
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response text: {response_text}")
            raise
        except Exception as e:
            logger.error(f"Error parsing interpretation response: {e}")
            raise
    
    def _create_fallback_interpretation(self, clinical_data: Dict[str, Any]) -> ClinicalInterpretation:
        """Create fallback interpretation when AI analysis fails"""
        return ClinicalInterpretation(
            summary="Clinical data extracted successfully. AI interpretation temporarily unavailable.",
            abnormalities={"critical": [], "significant": [], "minor": []},
            interpretation="Please review the extracted clinical data manually. AI interpretation service is currently unavailable.",
            recommendations=["Manual review of clinical data recommended", "Consult with healthcare provider"],
            differential_diagnoses=["Manual clinical assessment required"],
            follow_up_tests=["As clinically indicated"],
            clinical_significance="Unable to assess automatically - manual review required",
            confidence_score=0.0,
            citations=[]
        )
    
    def analyze_lab_values(self, lab_data: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze laboratory values for abnormalities
        
        Args:
            lab_data: Dictionary of lab test names and values
            
        Returns:
            Analysis of lab values with normal ranges and interpretations
        """
        if not lab_data:
            return {"analysis": "No laboratory data available for analysis"}
        
        prompt = f"""
Analyze the following laboratory values and provide clinical interpretation:

LABORATORY DATA:
{json.dumps(lab_data, indent=2)}

Please provide analysis in JSON format:
{{
    "abnormal_values": [
        {{
            "test": "Test name",
            "value": "Reported value",
            "normal_range": "Normal reference range",
            "interpretation": "Clinical significance",
            "severity": "mild/moderate/severe"
        }}
    ],
    "normal_values": ["List of tests within normal limits"],
    "clinical_patterns": "Overall pattern analysis",
    "recommendations": ["Specific recommendations based on lab findings"]
}}

Focus on identifying abnormal values, their clinical significance, and appropriate follow-up.
"""
        
        try:
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=2048,
                    response_mime_type="application/json"
                )
            )
            
            return json.loads(response.text)
            
        except Exception as e:
            logger.error(f"Error analyzing lab values: {e}")
            return {"error": "Lab analysis temporarily unavailable"}
    
    def generate_clinical_summary(self, 
                                full_report_text: str,
                                structured_data: Dict[str, Any]) -> str:
        """
        Generate a concise clinical summary
        
        Args:
            full_report_text: Complete medical report text
            structured_data: Extracted structured clinical data
            
        Returns:
            Concise clinical summary
        """
        prompt = f"""
Generate a concise clinical summary based on the following medical report:

FULL REPORT:
{full_report_text[:2000]}...

STRUCTURED DATA:
{json.dumps(structured_data, indent=2)}

Provide a 3-4 sentence clinical summary that captures:
1. Patient demographics and chief complaint
2. Key findings and abnormalities
3. Primary diagnosis or clinical impression
4. Critical next steps or recommendations

Keep the summary professional, accurate, and focused on the most clinically relevant information.
"""
        
        try:
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=512
                )
            )
            
            return response.text.strip()
            
        except Exception as e:
            logger.error(f"Error generating clinical summary: {e}")
            return "Clinical summary generation temporarily unavailable."
