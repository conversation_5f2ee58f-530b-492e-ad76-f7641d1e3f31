"""
Main orchestration pipeline for MedInsight-Agent
"""
import logging
import json
import time
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict

from src.agents.medical_agents import MedicalAgentCrew
from src.chains.medical_chains import MedicalProcessingChains
from src.tools.pdf_processor import PDFProcessor
from src.tools.clinical_nlp import ClinicalNLPProcessor
from src.models.gemini_client import GeminiClinicalClient
from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ProcessingResult:
    """Data class for pipeline processing results"""
    success: bool
    processing_time: float
    pdf_extraction: Dict[str, Any]
    clinical_entities: Dict[str, Any]
    clinical_interpretation: Dict[str, Any]
    recommendations: List[str]
    confidence_score: float
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class MedicalPipeline:
    """
    Main orchestration pipeline for medical report analysis
    """
    
    def __init__(self, use_crewai: bool = True, use_langchain: bool = True):
        """
        Initialize the medical pipeline
        
        Args:
            use_crewai: Whether to use CrewAI for agent orchestration
            use_langchain: Whether to use Lang<PERSON>hain for memory and RAG
        """
        self.config = Config()
        self.use_crewai = use_crewai
        self.use_langchain = use_langchain
        
        # Initialize components
        self._initialize_components()
        
        logger.info(f"MedicalPipeline initialized (CrewAI: {use_crewai}, LangChain: {use_langchain})")
    
    def _initialize_components(self):
        """Initialize pipeline components"""
        try:
            # Core tools (always available)
            self.pdf_processor = PDFProcessor()
            self.nlp_processor = ClinicalNLPProcessor()
            self.gemini_client = GeminiClinicalClient()
            
            # CrewAI agents (optional)
            if self.use_crewai:
                self.agent_crew = MedicalAgentCrew()
                logger.info("CrewAI agents initialized")
            
            # LangChain chains (optional)
            if self.use_langchain:
                self.langchain_processor = MedicalProcessingChains()
                logger.info("LangChain chains initialized")
                
        except Exception as e:
            logger.error(f"Error initializing pipeline components: {e}")
            raise
    
    def process_medical_report(self, 
                             pdf_content: Union[bytes, str, Path],
                             patient_context: Optional[Dict[str, Any]] = None,
                             processing_mode: str = "comprehensive") -> ProcessingResult:
        """
        Process a medical report through the complete pipeline
        
        Args:
            pdf_content: PDF file content (bytes, file path, or Path object)
            patient_context: Optional patient context information
            processing_mode: Processing mode ('fast', 'comprehensive', 'research')
            
        Returns:
            ProcessingResult with complete analysis
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting medical report processing (mode: {processing_mode})")
            
            # Step 1: PDF Processing and Text Extraction
            pdf_result = self._process_pdf(pdf_content)
            if not pdf_result["success"]:
                return ProcessingResult(
                    success=False,
                    processing_time=time.time() - start_time,
                    pdf_extraction={},
                    clinical_entities={},
                    clinical_interpretation={},
                    recommendations=[],
                    confidence_score=0.0,
                    error_message=f"PDF processing failed: {pdf_result.get('error', 'Unknown error')}"
                )
            
            # Step 2: Clinical Entity Extraction
            clinical_entities = self._extract_clinical_entities(pdf_result["processed_text"])
            
            # Step 3: Clinical Interpretation
            interpretation_result = self._generate_clinical_interpretation(
                clinical_entities, 
                patient_context,
                processing_mode
            )
            
            # Step 4: Generate Final Recommendations
            recommendations = self._generate_recommendations(
                interpretation_result,
                clinical_entities,
                patient_context
            )
            
            # Calculate overall confidence score
            confidence_score = self._calculate_confidence_score(
                pdf_result,
                clinical_entities,
                interpretation_result
            )
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                success=True,
                processing_time=processing_time,
                pdf_extraction=pdf_result,
                clinical_entities=clinical_entities,
                clinical_interpretation=interpretation_result,
                recommendations=recommendations,
                confidence_score=confidence_score,
                metadata={
                    "processing_mode": processing_mode,
                    "use_crewai": self.use_crewai,
                    "use_langchain": self.use_langchain,
                    "timestamp": time.time()
                }
            )
            
            logger.info(f"Medical report processing completed in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Error in medical report processing: {e}")
            return ProcessingResult(
                success=False,
                processing_time=time.time() - start_time,
                pdf_extraction={},
                clinical_entities={},
                clinical_interpretation={},
                recommendations=[],
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def _process_pdf(self, pdf_content: Union[bytes, str, Path]) -> Dict[str, Any]:
        """Process PDF using appropriate method"""
        
        # Convert Path to string if needed
        if isinstance(pdf_content, Path):
            pdf_content = str(pdf_content)
        
        if self.use_crewai:
            # Use CrewAI agent for PDF processing
            try:
                crew_result = self.agent_crew.process_medical_report(pdf_content)
                if crew_result["success"]:
                    # Extract PDF processing result from crew output
                    return {
                        "success": True,
                        "processed_text": str(crew_result["result"]),
                        "method": "crewai",
                        "metadata": crew_result.get("crew_output", {})
                    }
                else:
                    # Fallback to direct processing
                    return self.pdf_processor.process_medical_pdf(pdf_content)
            except Exception as e:
                logger.warning(f"CrewAI PDF processing failed, using fallback: {e}")
                return self.pdf_processor.process_medical_pdf(pdf_content)
        else:
            # Use direct PDF processor
            return self.pdf_processor.process_medical_pdf(pdf_content)
    
    def _extract_clinical_entities(self, text: str) -> Dict[str, Any]:
        """Extract clinical entities from text"""
        
        if self.use_langchain:
            # Use LangChain for entity extraction with memory
            try:
                langchain_result = self.langchain_processor.process_pdf_content(text)
                if langchain_result["success"]:
                    # Also run NLP processor for structured data
                    nlp_result = self.nlp_processor.extract_structured_data(text)
                    
                    return {
                        "langchain_processing": langchain_result["processed_content"],
                        "structured_entities": nlp_result,
                        "method": "langchain_nlp"
                    }
            except Exception as e:
                logger.warning(f"LangChain processing failed, using fallback: {e}")
        
        # Fallback to direct NLP processing
        return self.nlp_processor.extract_structured_data(text)
    
    def _generate_clinical_interpretation(self, 
                                        clinical_entities: Dict[str, Any],
                                        patient_context: Optional[Dict[str, Any]],
                                        processing_mode: str) -> Dict[str, Any]:
        """Generate clinical interpretation"""
        
        if self.use_langchain and processing_mode in ["comprehensive", "research"]:
            # Use LangChain for enhanced interpretation with RAG
            try:
                analysis_result = self.langchain_processor.analyze_clinical_data(
                    clinical_entities,
                    retrieve_guidelines=(processing_mode == "research")
                )
                
                if analysis_result["success"]:
                    return {
                        "langchain_analysis": analysis_result["analysis"],
                        "guidelines_used": analysis_result["guidelines_used"],
                        "method": "langchain_rag"
                    }
            except Exception as e:
                logger.warning(f"LangChain analysis failed, using fallback: {e}")
        
        # Fallback to direct Gemini interpretation
        interpretation = self.gemini_client.interpret_clinical_data(
            clinical_entities,
            context=str(patient_context) if patient_context else None
        )
        
        return {
            "summary": interpretation.summary,
            "abnormalities": interpretation.abnormalities,
            "interpretation": interpretation.interpretation,
            "differential_diagnoses": interpretation.differential_diagnoses,
            "follow_up_tests": interpretation.follow_up_tests,
            "clinical_significance": interpretation.clinical_significance,
            "confidence_score": interpretation.confidence_score,
            "citations": interpretation.citations,
            "method": "gemini_direct"
        }
    
    def _generate_recommendations(self, 
                                interpretation_result: Dict[str, Any],
                                clinical_entities: Dict[str, Any],
                                patient_context: Optional[Dict[str, Any]]) -> List[str]:
        """Generate final recommendations"""
        
        if self.use_langchain:
            # Use LangChain for recommendation generation
            try:
                rec_result = self.langchain_processor.generate_recommendations(
                    analysis=str(interpretation_result),
                    patient_context=patient_context or {},
                    retrieve_guidelines=True
                )
                
                if rec_result["success"]:
                    # Parse recommendations from LangChain output
                    recommendations_text = rec_result["recommendations"]
                    # Simple parsing - in production, you might want more sophisticated parsing
                    recommendations = [
                        line.strip("- ").strip() 
                        for line in recommendations_text.split("\n") 
                        if line.strip().startswith("-") or line.strip().startswith("•")
                    ]
                    
                    if recommendations:
                        return recommendations
            except Exception as e:
                logger.warning(f"LangChain recommendations failed, using fallback: {e}")
        
        # Fallback to recommendations from interpretation
        recommendations = []
        
        if "recommendations" in interpretation_result:
            recommendations.extend(interpretation_result["recommendations"])
        
        if "follow_up_tests" in interpretation_result:
            recommendations.extend([
                f"Follow-up: {test}" for test in interpretation_result["follow_up_tests"]
            ])
        
        # Add default recommendations if none found
        if not recommendations:
            recommendations = [
                "Review findings with healthcare provider",
                "Follow standard clinical protocols",
                "Monitor patient status as appropriate"
            ]
        
        return recommendations
    
    def _calculate_confidence_score(self, 
                                  pdf_result: Dict[str, Any],
                                  clinical_entities: Dict[str, Any],
                                  interpretation_result: Dict[str, Any]) -> float:
        """Calculate overall confidence score for the analysis"""
        
        scores = []
        
        # PDF extraction confidence
        if "metadata" in pdf_result and "average_confidence" in pdf_result["metadata"]:
            scores.append(pdf_result["metadata"]["average_confidence"] / 100.0)
        else:
            scores.append(0.8)  # Default for non-OCR extraction
        
        # Clinical entity extraction confidence
        if "raw_entities" in clinical_entities:
            entity_confidences = []
            for entity_type, entities in clinical_entities["raw_entities"].items():
                if entities:
                    avg_confidence = sum(getattr(e, 'confidence', 0.8) for e in entities) / len(entities)
                    entity_confidences.append(avg_confidence)
            
            if entity_confidences:
                scores.append(sum(entity_confidences) / len(entity_confidences))
            else:
                scores.append(0.7)
        else:
            scores.append(0.7)
        
        # Interpretation confidence
        if "confidence_score" in interpretation_result:
            scores.append(interpretation_result["confidence_score"])
        else:
            scores.append(0.75)
        
        # Calculate weighted average
        return sum(scores) / len(scores) if scores else 0.5
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get status of pipeline components"""
        status = {
            "pdf_processor": "Ready",
            "nlp_processor": "Ready",
            "gemini_client": "Ready",
            "crewai_agents": "Not used",
            "langchain_chains": "Not used"
        }
        
        if self.use_crewai:
            try:
                agent_status = self.agent_crew.get_agent_status()
                status["crewai_agents"] = agent_status
            except:
                status["crewai_agents"] = "Error"
        
        if self.use_langchain:
            try:
                # Simple check for LangChain components
                status["langchain_chains"] = "Ready"
            except:
                status["langchain_chains"] = "Error"
        
        return status
