"""
Clinical NLP tools for medical entity extraction and processing
"""
import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

import spacy
import pandas as pd
from transformers import AutoTokenizer, AutoModel
import torch

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class ClinicalEntity:
    """Data class for clinical entities"""
    text: str
    label: str
    start: int
    end: int
    confidence: float = 0.0
    normalized_form: str = ""
    
class ClinicalNLPProcessor:
    """
    Clinical NLP processor for extracting medical entities and information
    """
    
    def __init__(self):
        self.config = Config()
        self.nlp = None
        self.clinical_model = None
        self.tokenizer = None
        self._load_models()
        
    def _load_models(self):
        """Load required NLP models"""
        try:
            # Load spaCy clinical model
            try:
                self.nlp = spacy.load(self.config.SPACY_MODEL)
                logger.info(f"Loaded spaCy model: {self.config.SPACY_MODEL}")
            except OSError:
                logger.warning(f"Clinical spaCy model {self.config.SPACY_MODEL} not found. Using en_core_web_sm")
                self.nlp = spacy.load("en_core_web_sm")
            
            # Load Clinical BERT for advanced processing
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(self.config.CLINICAL_BERT_MODEL)
                self.clinical_model = AutoModel.from_pretrained(self.config.CLINICAL_BERT_MODEL)
                logger.info(f"Loaded Clinical BERT: {self.config.CLINICAL_BERT_MODEL}")
            except Exception as e:
                logger.warning(f"Could not load Clinical BERT: {e}")
                
        except Exception as e:
            logger.error(f"Error loading NLP models: {e}")
            raise
    
    def extract_clinical_entities(self, text: str) -> Dict[str, List[ClinicalEntity]]:
        """
        Extract clinical entities from medical text
        
        Args:
            text: Medical text to process
            
        Returns:
            Dictionary of entity types and their instances
        """
        if not self.nlp:
            raise ValueError("NLP model not loaded")
            
        doc = self.nlp(text)
        
        entities = {
            "medications": [],
            "conditions": [],
            "procedures": [],
            "anatomy": [],
            "lab_values": [],
            "vital_signs": [],
            "dates": [],
            "persons": [],
            "organizations": []
        }
        
        # Extract named entities
        for ent in doc.ents:
            entity = ClinicalEntity(
                text=ent.text,
                label=ent.label_,
                start=ent.start_char,
                end=ent.end_char,
                confidence=getattr(ent, 'confidence', 0.8)
            )
            
            # Categorize entities
            if ent.label_ in ["DRUG", "MEDICATION"]:
                entities["medications"].append(entity)
            elif ent.label_ in ["DISEASE", "CONDITION", "SYMPTOM"]:
                entities["conditions"].append(entity)
            elif ent.label_ in ["PROCEDURE", "TREATMENT"]:
                entities["procedures"].append(entity)
            elif ent.label_ in ["ANATOMY", "BODY_PART"]:
                entities["anatomy"].append(entity)
            elif ent.label_ in ["DATE", "TIME"]:
                entities["dates"].append(entity)
            elif ent.label_ in ["PERSON", "PATIENT"]:
                entities["persons"].append(entity)
            elif ent.label_ in ["ORG", "HOSPITAL", "CLINIC"]:
                entities["organizations"].append(entity)
        
        # Extract lab values and vital signs using regex patterns
        entities["lab_values"].extend(self._extract_lab_values(text))
        entities["vital_signs"].extend(self._extract_vital_signs(text))
        
        return entities
    
    def _extract_lab_values(self, text: str) -> List[ClinicalEntity]:
        """Extract laboratory values using pattern matching"""
        lab_patterns = [
            # Common lab tests with values
            (r'(?i)(hemoglobin|hgb|hb)\s*:?\s*(\d+\.?\d*)\s*(g/dl|mg/dl)?', 'LAB_HEMOGLOBIN'),
            (r'(?i)(hematocrit|hct)\s*:?\s*(\d+\.?\d*)\s*%?', 'LAB_HEMATOCRIT'),
            (r'(?i)(glucose|blood sugar)\s*:?\s*(\d+\.?\d*)\s*(mg/dl|mmol/l)?', 'LAB_GLUCOSE'),
            (r'(?i)(creatinine)\s*:?\s*(\d+\.?\d*)\s*(mg/dl|μmol/l)?', 'LAB_CREATININE'),
            (r'(?i)(bun|blood urea nitrogen)\s*:?\s*(\d+\.?\d*)\s*(mg/dl)?', 'LAB_BUN'),
            (r'(?i)(cholesterol)\s*:?\s*(\d+\.?\d*)\s*(mg/dl)?', 'LAB_CHOLESTEROL'),
            (r'(?i)(triglycerides)\s*:?\s*(\d+\.?\d*)\s*(mg/dl)?', 'LAB_TRIGLYCERIDES'),
            (r'(?i)(sodium|na)\s*:?\s*(\d+\.?\d*)\s*(meq/l|mmol/l)?', 'LAB_SODIUM'),
            (r'(?i)(potassium|k)\s*:?\s*(\d+\.?\d*)\s*(meq/l|mmol/l)?', 'LAB_POTASSIUM'),
            (r'(?i)(chloride|cl)\s*:?\s*(\d+\.?\d*)\s*(meq/l|mmol/l)?', 'LAB_CHLORIDE'),
        ]
        
        lab_values = []
        for pattern, label in lab_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entity = ClinicalEntity(
                    text=match.group(0),
                    label=label,
                    start=match.start(),
                    end=match.end(),
                    confidence=0.9
                )
                lab_values.append(entity)
        
        return lab_values
    
    def _extract_vital_signs(self, text: str) -> List[ClinicalEntity]:
        """Extract vital signs using pattern matching"""
        vital_patterns = [
            (r'(?i)(blood pressure|bp)\s*:?\s*(\d+/\d+)\s*(mmhg)?', 'VITAL_BP'),
            (r'(?i)(heart rate|hr|pulse)\s*:?\s*(\d+)\s*(bpm)?', 'VITAL_HR'),
            (r'(?i)(temperature|temp)\s*:?\s*(\d+\.?\d*)\s*(°?f|°?c|fahrenheit|celsius)?', 'VITAL_TEMP'),
            (r'(?i)(respiratory rate|rr|respiration)\s*:?\s*(\d+)\s*(per min|/min)?', 'VITAL_RR'),
            (r'(?i)(oxygen saturation|o2 sat|spo2)\s*:?\s*(\d+\.?\d*)\s*%?', 'VITAL_O2SAT'),
            (r'(?i)(weight)\s*:?\s*(\d+\.?\d*)\s*(kg|lbs|pounds)?', 'VITAL_WEIGHT'),
            (r'(?i)(height)\s*:?\s*(\d+\.?\d*)\s*(cm|ft|inches|in)?', 'VITAL_HEIGHT'),
            (r'(?i)(bmi|body mass index)\s*:?\s*(\d+\.?\d*)', 'VITAL_BMI'),
        ]
        
        vital_signs = []
        for pattern, label in vital_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entity = ClinicalEntity(
                    text=match.group(0),
                    label=label,
                    start=match.start(),
                    end=match.end(),
                    confidence=0.95
                )
                vital_signs.append(entity)
        
        return vital_signs
    
    def extract_structured_data(self, text: str) -> Dict[str, Any]:
        """
        Extract structured clinical data from medical text
        
        Args:
            text: Medical text to process
            
        Returns:
            Structured dictionary of clinical information
        """
        entities = self.extract_clinical_entities(text)
        
        # Structure the data
        structured_data = {
            "patient_info": self._extract_patient_info(text),
            "chief_complaint": self._extract_chief_complaint(text),
            "history_present_illness": self._extract_hpi(text),
            "medications": [entity.text for entity in entities["medications"]],
            "conditions": [entity.text for entity in entities["conditions"]],
            "procedures": [entity.text for entity in entities["procedures"]],
            "lab_results": self._structure_lab_results(entities["lab_values"]),
            "vital_signs": self._structure_vital_signs(entities["vital_signs"]),
            "physical_exam": self._extract_physical_exam(text),
            "assessment_plan": self._extract_assessment_plan(text),
            "raw_entities": entities
        }
        
        return structured_data
    
    def _extract_patient_info(self, text: str) -> Dict[str, str]:
        """Extract patient demographic information"""
        patient_info = {}
        
        # Age pattern
        age_match = re.search(r'(?i)(\d+)\s*(?:year|yr|y\.?o\.?)\s*(?:old)?', text)
        if age_match:
            patient_info["age"] = age_match.group(1)
        
        # Gender pattern
        gender_match = re.search(r'(?i)(male|female|m|f)\s*(?:patient)?', text)
        if gender_match:
            patient_info["gender"] = gender_match.group(1)
        
        return patient_info
    
    def _extract_chief_complaint(self, text: str) -> str:
        """Extract chief complaint from medical text"""
        cc_patterns = [
            r'(?i)chief\s+complaint\s*:?\s*(.+?)(?:\n|\.|\s{2,})',
            r'(?i)cc\s*:?\s*(.+?)(?:\n|\.|\s{2,})',
            r'(?i)presenting\s+complaint\s*:?\s*(.+?)(?:\n|\.|\s{2,})'
        ]
        
        for pattern in cc_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_hpi(self, text: str) -> str:
        """Extract History of Present Illness"""
        hpi_patterns = [
            r'(?i)history\s+of\s+present\s+illness\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)',
            r'(?i)hpi\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)'
        ]
        
        for pattern in hpi_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_physical_exam(self, text: str) -> str:
        """Extract physical examination findings"""
        pe_patterns = [
            r'(?i)physical\s+exam(?:ination)?\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)',
            r'(?i)pe\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)'
        ]
        
        for pattern in pe_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _extract_assessment_plan(self, text: str) -> str:
        """Extract assessment and plan"""
        ap_patterns = [
            r'(?i)assessment\s+and\s+plan\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)',
            r'(?i)a&p\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)',
            r'(?i)plan\s*:?\s*(.+?)(?=\n\s*[A-Z]|\n\n|$)'
        ]
        
        for pattern in ap_patterns:
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def _structure_lab_results(self, lab_entities: List[ClinicalEntity]) -> Dict[str, str]:
        """Structure lab results into a dictionary"""
        lab_results = {}
        for entity in lab_entities:
            # Extract test name and value
            parts = entity.text.split(':')
            if len(parts) >= 2:
                test_name = parts[0].strip()
                value = parts[1].strip()
                lab_results[test_name] = value
            else:
                lab_results[entity.label] = entity.text
        
        return lab_results
    
    def _structure_vital_signs(self, vital_entities: List[ClinicalEntity]) -> Dict[str, str]:
        """Structure vital signs into a dictionary"""
        vital_signs = {}
        for entity in vital_entities:
            # Extract vital sign name and value
            parts = entity.text.split(':')
            if len(parts) >= 2:
                vital_name = parts[0].strip()
                value = parts[1].strip()
                vital_signs[vital_name] = value
            else:
                vital_signs[entity.label] = entity.text
        
        return vital_signs
