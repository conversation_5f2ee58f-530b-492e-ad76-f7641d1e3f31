"""
PDF processing and OCR tools for medical documents
"""
import io
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

import fitz  # PyMuPDF
import pytesseract
from PIL import Image
from pdf2image import convert_from_bytes, convert_from_path
from pdfminer.high_level import extract_text
from pdfminer.layout import LAParams

from config import Config

logger = logging.getLogger(__name__)

class PDFProcessor:
    """
    Comprehensive PDF processing tool with multiple extraction methods
    """
    
    def __init__(self):
        self.config = Config()
        
    def extract_text_pymupdf(self, pdf_content: Union[bytes, str]) -> Dict[str, any]:
        """
        Extract text using PyMuPDF (fastest method)
        
        Args:
            pdf_content: PDF file as bytes or file path
            
        Returns:
            Dict containing extracted text and metadata
        """
        try:
            if isinstance(pdf_content, str):
                doc = fitz.open(pdf_content)
            else:
                doc = fitz.open(stream=pdf_content, filetype="pdf")
            
            text_content = []
            metadata = {
                "page_count": len(doc),
                "title": doc.metadata.get("title", ""),
                "author": doc.metadata.get("author", ""),
                "creation_date": doc.metadata.get("creationDate", ""),
                "extraction_method": "pymupdf"
            }
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()
                
                if text.strip():
                    text_content.append({
                        "page": page_num + 1,
                        "text": text.strip(),
                        "char_count": len(text)
                    })
                    
            doc.close()
            
            return {
                "success": True,
                "text_content": text_content,
                "metadata": metadata,
                "full_text": "\n\n".join([page["text"] for page in text_content])
            }
            
        except Exception as e:
            logger.error(f"PyMuPDF extraction failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def extract_text_pdfminer(self, pdf_content: Union[bytes, str]) -> Dict[str, any]:
        """
        Extract text using pdfminer (better for complex layouts)
        
        Args:
            pdf_content: PDF file as bytes or file path
            
        Returns:
            Dict containing extracted text and metadata
        """
        try:
            if isinstance(pdf_content, str):
                with open(pdf_content, 'rb') as file:
                    pdf_bytes = file.read()
            else:
                pdf_bytes = pdf_content
                
            # Configure layout analysis parameters
            laparams = LAParams(
                boxes_flow=0.5,
                word_margin=0.1,
                char_margin=2.0,
                line_margin=0.5
            )
            
            text = extract_text(io.BytesIO(pdf_bytes), laparams=laparams)
            
            return {
                "success": True,
                "full_text": text.strip(),
                "metadata": {
                    "extraction_method": "pdfminer",
                    "char_count": len(text)
                }
            }
            
        except Exception as e:
            logger.error(f"PDFMiner extraction failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def extract_text_ocr(self, pdf_content: Union[bytes, str], dpi: int = 300) -> Dict[str, any]:
        """
        Extract text using OCR (for scanned documents)
        
        Args:
            pdf_content: PDF file as bytes or file path
            dpi: Resolution for image conversion
            
        Returns:
            Dict containing extracted text and metadata
        """
        try:
            # Convert PDF to images
            if isinstance(pdf_content, str):
                images = convert_from_path(pdf_content, dpi=dpi)
            else:
                images = convert_from_bytes(pdf_content, dpi=dpi)
            
            text_content = []
            total_confidence = 0
            
            for page_num, image in enumerate(images):
                # Perform OCR with confidence scores
                ocr_data = pytesseract.image_to_data(
                    image, 
                    config=self.config.TESSERACT_CONFIG,
                    output_type=pytesseract.Output.DICT
                )
                
                # Extract text and calculate confidence
                page_text = []
                confidences = []
                
                for i, word in enumerate(ocr_data['text']):
                    if word.strip():
                        page_text.append(word)
                        confidences.append(int(ocr_data['conf'][i]))
                
                page_text_str = ' '.join(page_text)
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                
                if page_text_str.strip():
                    text_content.append({
                        "page": page_num + 1,
                        "text": page_text_str.strip(),
                        "confidence": avg_confidence,
                        "char_count": len(page_text_str)
                    })
                    
                total_confidence += avg_confidence
            
            avg_total_confidence = total_confidence / len(text_content) if text_content else 0
            
            return {
                "success": True,
                "text_content": text_content,
                "metadata": {
                    "extraction_method": "ocr",
                    "page_count": len(images),
                    "average_confidence": avg_total_confidence,
                    "dpi": dpi
                },
                "full_text": "\n\n".join([page["text"] for page in text_content])
            }
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def extract_text_hybrid(self, pdf_content: Union[bytes, str]) -> Dict[str, any]:
        """
        Hybrid extraction: try PyMuPDF first, fallback to OCR if needed
        
        Args:
            pdf_content: PDF file as bytes or file path
            
        Returns:
            Dict containing extracted text and metadata
        """
        # First try PyMuPDF
        result = self.extract_text_pymupdf(pdf_content)
        
        if result["success"] and result["full_text"].strip():
            # Check if extracted text seems reasonable (not just gibberish)
            text_length = len(result["full_text"])
            if text_length > 100:  # Minimum reasonable length
                logger.info("PyMuPDF extraction successful")
                return result
        
        logger.info("PyMuPDF extraction insufficient, trying OCR...")
        
        # Fallback to OCR
        ocr_result = self.extract_text_ocr(pdf_content)
        
        if ocr_result["success"]:
            ocr_result["metadata"]["fallback_used"] = True
            ocr_result["metadata"]["primary_method"] = "pymupdf"
            ocr_result["metadata"]["fallback_method"] = "ocr"
            
        return ocr_result
    
    def process_medical_pdf(self, pdf_content: Union[bytes, str]) -> Dict[str, any]:
        """
        Main method for processing medical PDFs with optimized extraction
        
        Args:
            pdf_content: PDF file as bytes or file path
            
        Returns:
            Dict containing processed text and metadata
        """
        logger.info("Starting medical PDF processing...")
        
        # Use hybrid extraction for best results
        result = self.extract_text_hybrid(pdf_content)
        
        if not result["success"]:
            return result
        
        # Post-process the extracted text for medical documents
        processed_text = self._clean_medical_text(result["full_text"])
        
        result["processed_text"] = processed_text
        result["metadata"]["processing_complete"] = True
        
        logger.info(f"PDF processing complete. Extracted {len(processed_text)} characters.")
        
        return result
    
    def _clean_medical_text(self, text: str) -> str:
        """
        Clean and normalize medical text
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        import re
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Fix common OCR errors in medical text
        replacements = {
            r'\bpatient\s+name\b': 'Patient Name',
            r'\bdate\s+of\s+birth\b': 'Date of Birth',
            r'\bmedical\s+record\s+number\b': 'Medical Record Number',
            r'\bchief\s+complaint\b': 'Chief Complaint',
            r'\bhistory\s+of\s+present\s+illness\b': 'History of Present Illness',
            r'\bphysical\s+examination\b': 'Physical Examination',
            r'\bassessment\s+and\s+plan\b': 'Assessment and Plan',
        }
        
        for pattern, replacement in replacements.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text.strip()
