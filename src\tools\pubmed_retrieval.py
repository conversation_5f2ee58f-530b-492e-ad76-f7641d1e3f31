"""
PubMed retrieval tool for clinical guidelines and research
"""
import logging
import requests
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import xml.etree.ElementTree as ET

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class PubMedArticle:
    """Data class for PubMed articles"""
    pmid: str
    title: str
    abstract: str
    authors: List[str]
    journal: str
    publication_date: str
    doi: str = ""
    keywords: List[str] = None
    relevance_score: float = 0.0

class PubMedRetriever:
    """
    PubMed retrieval tool for finding relevant clinical literature
    """
    
    def __init__(self):
        self.config = Config()
        self.base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
        self.api_key = self.config.PUBMED_API_KEY
        self.email = self.config.PUBMED_EMAIL
        
    def search_articles(self, 
                       query: str, 
                       max_results: int = 10,
                       sort_by: str = "relevance",
                       publication_types: List[str] = None) -> List[PubMedArticle]:
        """
        Search PubMed for articles related to clinical query
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            sort_by: Sort criteria (relevance, date, etc.)
            publication_types: Filter by publication types
            
        Returns:
            List of PubMedArticle objects
        """
        try:
            # Step 1: Search for PMIDs
            pmids = self._search_pmids(query, max_results, sort_by, publication_types)
            
            if not pmids:
                logger.warning(f"No articles found for query: {query}")
                return []
            
            # Step 2: Fetch article details
            articles = self._fetch_article_details(pmids)
            
            # Step 3: Calculate relevance scores
            articles = self._calculate_relevance_scores(articles, query)
            
            # Sort by relevance score
            articles.sort(key=lambda x: x.relevance_score, reverse=True)
            
            logger.info(f"Retrieved {len(articles)} articles for query: {query}")
            return articles
            
        except Exception as e:
            logger.error(f"Error searching PubMed: {e}")
            return []
    
    def _search_pmids(self, 
                     query: str, 
                     max_results: int,
                     sort_by: str,
                     publication_types: List[str]) -> List[str]:
        """Search for PMIDs using eSearch"""
        
        # Build search URL
        search_url = f"{self.base_url}esearch.fcgi"
        
        # Enhance query with medical terms and filters
        enhanced_query = self._enhance_clinical_query(query, publication_types)
        
        params = {
            "db": "pubmed",
            "term": enhanced_query,
            "retmax": max_results,
            "sort": sort_by,
            "retmode": "xml"
        }
        
        if self.api_key:
            params["api_key"] = self.api_key
        if self.email:
            params["email"] = self.email
            
        try:
            response = requests.get(search_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            pmids = [id_elem.text for id_elem in root.findall(".//Id")]
            
            return pmids
            
        except Exception as e:
            logger.error(f"Error in PubMed search: {e}")
            return []
    
    def _fetch_article_details(self, pmids: List[str]) -> List[PubMedArticle]:
        """Fetch detailed information for articles using eFetch"""
        
        if not pmids:
            return []
        
        # Batch fetch (max 200 at a time)
        batch_size = 200
        all_articles = []
        
        for i in range(0, len(pmids), batch_size):
            batch_pmids = pmids[i:i + batch_size]
            batch_articles = self._fetch_batch_details(batch_pmids)
            all_articles.extend(batch_articles)
            
            # Rate limiting
            time.sleep(0.1)
        
        return all_articles
    
    def _fetch_batch_details(self, pmids: List[str]) -> List[PubMedArticle]:
        """Fetch details for a batch of PMIDs"""
        
        fetch_url = f"{self.base_url}efetch.fcgi"
        
        params = {
            "db": "pubmed",
            "id": ",".join(pmids),
            "retmode": "xml",
            "rettype": "abstract"
        }
        
        if self.api_key:
            params["api_key"] = self.api_key
        if self.email:
            params["email"] = self.email
            
        try:
            response = requests.get(fetch_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.content)
            articles = []
            
            for article_elem in root.findall(".//PubmedArticle"):
                article = self._parse_article_xml(article_elem)
                if article:
                    articles.append(article)
            
            return articles
            
        except Exception as e:
            logger.error(f"Error fetching article details: {e}")
            return []
    
    def _parse_article_xml(self, article_elem: ET.Element) -> Optional[PubMedArticle]:
        """Parse individual article XML"""
        try:
            # Extract PMID
            pmid_elem = article_elem.find(".//PMID")
            pmid = pmid_elem.text if pmid_elem is not None else ""
            
            # Extract title
            title_elem = article_elem.find(".//ArticleTitle")
            title = title_elem.text if title_elem is not None else ""
            
            # Extract abstract
            abstract_parts = []
            for abstract_elem in article_elem.findall(".//AbstractText"):
                if abstract_elem.text:
                    label = abstract_elem.get("Label", "")
                    text = abstract_elem.text
                    if label:
                        abstract_parts.append(f"{label}: {text}")
                    else:
                        abstract_parts.append(text)
            
            abstract = " ".join(abstract_parts)
            
            # Extract authors
            authors = []
            for author_elem in article_elem.findall(".//Author"):
                last_name = author_elem.find("LastName")
                first_name = author_elem.find("ForeName")
                if last_name is not None and first_name is not None:
                    authors.append(f"{first_name.text} {last_name.text}")
            
            # Extract journal
            journal_elem = article_elem.find(".//Journal/Title")
            journal = journal_elem.text if journal_elem is not None else ""
            
            # Extract publication date
            pub_date_elem = article_elem.find(".//PubDate")
            pub_date = ""
            if pub_date_elem is not None:
                year = pub_date_elem.find("Year")
                month = pub_date_elem.find("Month")
                day = pub_date_elem.find("Day")
                
                date_parts = []
                if year is not None:
                    date_parts.append(year.text)
                if month is not None:
                    date_parts.append(month.text)
                if day is not None:
                    date_parts.append(day.text)
                
                pub_date = "-".join(date_parts)
            
            # Extract DOI
            doi = ""
            for id_elem in article_elem.findall(".//ArticleId"):
                if id_elem.get("IdType") == "doi":
                    doi = id_elem.text
                    break
            
            # Extract keywords
            keywords = []
            for keyword_elem in article_elem.findall(".//Keyword"):
                if keyword_elem.text:
                    keywords.append(keyword_elem.text)
            
            return PubMedArticle(
                pmid=pmid,
                title=title,
                abstract=abstract,
                authors=authors,
                journal=journal,
                publication_date=pub_date,
                doi=doi,
                keywords=keywords
            )
            
        except Exception as e:
            logger.error(f"Error parsing article XML: {e}")
            return None
    
    def _enhance_clinical_query(self, query: str, publication_types: List[str] = None) -> str:
        """Enhance query with clinical terms and filters"""
        
        # Add clinical context
        enhanced_query = query
        
        # Add publication type filters
        if publication_types:
            type_filters = " OR ".join([f'"{pt}"[Publication Type]' for pt in publication_types])
            enhanced_query += f" AND ({type_filters})"
        else:
            # Default to high-quality publication types
            default_types = [
                "Clinical Trial", "Randomized Controlled Trial", 
                "Systematic Review", "Meta-Analysis", "Practice Guideline"
            ]
            type_filters = " OR ".join([f'"{pt}"[Publication Type]' for pt in default_types])
            enhanced_query += f" AND ({type_filters})"
        
        # Add recency filter (last 10 years)
        enhanced_query += " AND (\"2014/01/01\"[Date - Publication] : \"3000\"[Date - Publication])"
        
        # Add language filter
        enhanced_query += " AND English[Language]"
        
        return enhanced_query
    
    def _calculate_relevance_scores(self, articles: List[PubMedArticle], query: str) -> List[PubMedArticle]:
        """Calculate relevance scores for articles"""
        
        query_terms = query.lower().split()
        
        for article in articles:
            score = 0.0
            
            # Score based on title matches
            title_lower = article.title.lower()
            title_matches = sum(1 for term in query_terms if term in title_lower)
            score += title_matches * 3.0  # Title matches are weighted higher
            
            # Score based on abstract matches
            abstract_lower = article.abstract.lower()
            abstract_matches = sum(1 for term in query_terms if term in abstract_lower)
            score += abstract_matches * 1.0
            
            # Score based on keywords
            if article.keywords:
                keyword_text = " ".join(article.keywords).lower()
                keyword_matches = sum(1 for term in query_terms if term in keyword_text)
                score += keyword_matches * 2.0
            
            # Normalize score
            max_possible_score = len(query_terms) * 6.0  # 3 + 1 + 2
            article.relevance_score = score / max_possible_score if max_possible_score > 0 else 0.0
        
        return articles
    
    def get_clinical_guidelines(self, condition: str, max_results: int = 5) -> List[PubMedArticle]:
        """
        Get clinical guidelines for a specific condition
        
        Args:
            condition: Medical condition
            max_results: Maximum number of guidelines to return
            
        Returns:
            List of clinical guideline articles
        """
        query = f"{condition} clinical guidelines treatment management"
        publication_types = ["Practice Guideline", "Consensus Development Conference"]
        
        return self.search_articles(
            query=query,
            max_results=max_results,
            publication_types=publication_types
        )
    
    def get_diagnostic_criteria(self, condition: str, max_results: int = 5) -> List[PubMedArticle]:
        """
        Get diagnostic criteria for a specific condition
        
        Args:
            condition: Medical condition
            max_results: Maximum number of articles to return
            
        Returns:
            List of articles about diagnostic criteria
        """
        query = f"{condition} diagnostic criteria diagnosis"
        publication_types = ["Practice Guideline", "Systematic Review", "Meta-Analysis"]
        
        return self.search_articles(
            query=query,
            max_results=max_results,
            publication_types=publication_types
        )
