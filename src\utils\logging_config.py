"""
Logging configuration for MedInsight-Agent
"""
import logging
import logging.handlers
from pathlib import Path
from config import Config

def setup_logging():
    """Setup logging configuration"""
    config = Config()
    
    # Create logs directory if it doesn't exist
    config.LOGS_DIR.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Root logger
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format=log_format,
        handlers=[
            # Console handler
            logging.StreamHandler(),
            # File handler with rotation
            logging.handlers.RotatingFileHandler(
                config.LOGS_DIR / 'medinsight.log',
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
        ]
    )
    
    # Specific loggers
    loggers = [
        'src.pipeline.medical_pipeline',
        'src.agents.medical_agents',
        'src.chains.medical_chains',
        'src.tools.pdf_processor',
        'src.tools.clinical_nlp',
        'src.tools.pubmed_retrieval',
        'src.models.gemini_client'
    ]
    
    for logger_name in loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, config.LOG_LEVEL))
    
    # Suppress verbose third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('transformers').setLevel(logging.WARNING)
    
    logging.info("Logging configuration initialized")

if __name__ == "__main__":
    setup_logging()
