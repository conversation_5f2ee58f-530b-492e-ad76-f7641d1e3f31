"""
Unit tests for MedInsight-Agent pipeline
"""
import pytest
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.pipeline.medical_pipeline import MedicalPipeline, ProcessingResult
from src.tools.pdf_processor import PDFProcessor
from src.tools.clinical_nlp import ClinicalNLPProcessor
from src.models.gemini_client import GeminiClinicalClient

class TestMedicalPipeline:
    """Test cases for MedicalPipeline"""
    
    @pytest.fixture
    def sample_pdf_content(self):
        """Sample PDF content for testing"""
        return b"Sample PDF content for testing"
    
    @pytest.fixture
    def sample_clinical_data(self):
        """Sample clinical data for testing"""
        return {
            "patient_info": {"age": "45", "gender": "Male"},
            "chief_complaint": "Chest pain",
            "medications": ["Aspirin 81mg", "Metoprolol 50mg"],
            "conditions": ["Hypertension", "Chest pain"],
            "lab_results": {"Cholesterol": "220 mg/dl", "Glucose": "95 mg/dl"},
            "vital_signs": {"Blood Pressure": "140/90 mmHg", "Heart Rate": "72 bpm"}
        }
    
    @pytest.fixture
    def mock_pipeline(self):
        """Create a mock pipeline for testing"""
        with patch('src.pipeline.medical_pipeline.PDFProcessor'), \
             patch('src.pipeline.medical_pipeline.ClinicalNLPProcessor'), \
             patch('src.pipeline.medical_pipeline.GeminiClinicalClient'):
            pipeline = MedicalPipeline(use_crewai=False, use_langchain=False)
            return pipeline
    
    def test_pipeline_initialization(self):
        """Test pipeline initialization"""
        with patch('src.pipeline.medical_pipeline.PDFProcessor'), \
             patch('src.pipeline.medical_pipeline.ClinicalNLPProcessor'), \
             patch('src.pipeline.medical_pipeline.GeminiClinicalClient'):
            
            pipeline = MedicalPipeline(use_crewai=False, use_langchain=False)
            
            assert pipeline is not None
            assert pipeline.use_crewai is False
            assert pipeline.use_langchain is False
    
    def test_pdf_processing_success(self, mock_pipeline, sample_pdf_content):
        """Test successful PDF processing"""
        # Mock PDF processor
        mock_pipeline.pdf_processor.process_medical_pdf.return_value = {
            "success": True,
            "processed_text": "Sample medical report text",
            "metadata": {"extraction_method": "pymupdf"}
        }
        
        result = mock_pipeline._process_pdf(sample_pdf_content)
        
        assert result["success"] is True
        assert "processed_text" in result
        assert result["processed_text"] == "Sample medical report text"
    
    def test_clinical_entity_extraction(self, mock_pipeline, sample_clinical_data):
        """Test clinical entity extraction"""
        # Mock NLP processor
        mock_pipeline.nlp_processor.extract_structured_data.return_value = sample_clinical_data
        
        result = mock_pipeline._extract_clinical_entities("Sample medical text")
        
        assert result is not None
        assert "patient_info" in result
        assert "medications" in result
    
    def test_clinical_interpretation(self, mock_pipeline, sample_clinical_data):
        """Test clinical interpretation"""
        # Mock Gemini client
        mock_interpretation = Mock()
        mock_interpretation.summary = "Patient presents with chest pain and hypertension"
        mock_interpretation.abnormalities = {"critical": [], "significant": ["Elevated BP"], "minor": []}
        mock_interpretation.interpretation = "Hypertensive patient with chest pain"
        mock_interpretation.differential_diagnoses = ["Angina", "Hypertensive crisis"]
        mock_interpretation.follow_up_tests = ["ECG", "Stress test"]
        mock_interpretation.clinical_significance = "Moderate risk"
        mock_interpretation.confidence_score = 0.85
        mock_interpretation.citations = ["AHA Guidelines"]
        
        mock_pipeline.gemini_client.interpret_clinical_data.return_value = mock_interpretation
        
        result = mock_pipeline._generate_clinical_interpretation(
            sample_clinical_data, None, "comprehensive"
        )
        
        assert result is not None
        assert "summary" in result
        assert result["confidence_score"] == 0.85
    
    def test_full_pipeline_processing(self, sample_pdf_content):
        """Test full pipeline processing"""
        with patch('src.pipeline.medical_pipeline.PDFProcessor') as mock_pdf, \
             patch('src.pipeline.medical_pipeline.ClinicalNLPProcessor') as mock_nlp, \
             patch('src.pipeline.medical_pipeline.GeminiClinicalClient') as mock_gemini:
            
            # Setup mocks
            mock_pdf_instance = mock_pdf.return_value
            mock_pdf_instance.process_medical_pdf.return_value = {
                "success": True,
                "processed_text": "Sample medical report",
                "metadata": {}
            }
            
            mock_nlp_instance = mock_nlp.return_value
            mock_nlp_instance.extract_structured_data.return_value = {
                "medications": ["Aspirin"],
                "conditions": ["Hypertension"]
            }
            
            mock_gemini_instance = mock_gemini.return_value
            mock_interpretation = Mock()
            mock_interpretation.summary = "Test summary"
            mock_interpretation.abnormalities = {"critical": [], "significant": [], "minor": []}
            mock_interpretation.interpretation = "Test interpretation"
            mock_interpretation.differential_diagnoses = ["Test diagnosis"]
            mock_interpretation.follow_up_tests = ["Test follow-up"]
            mock_interpretation.clinical_significance = "Test significance"
            mock_interpretation.confidence_score = 0.8
            mock_interpretation.citations = []
            mock_gemini_instance.interpret_clinical_data.return_value = mock_interpretation
            
            # Create pipeline and process
            pipeline = MedicalPipeline(use_crewai=False, use_langchain=False)
            result = pipeline.process_medical_report(sample_pdf_content)
            
            assert result.success is True
            assert result.confidence_score > 0
            assert len(result.recommendations) > 0

class TestPDFProcessor:
    """Test cases for PDFProcessor"""
    
    def test_pdf_processor_initialization(self):
        """Test PDF processor initialization"""
        processor = PDFProcessor()
        assert processor is not None
    
    @patch('fitz.open')
    def test_pymupdf_extraction(self, mock_fitz):
        """Test PyMuPDF text extraction"""
        # Mock PyMuPDF document
        mock_doc = Mock()
        mock_doc.__len__.return_value = 1
        mock_doc.metadata = {"title": "Test Report"}
        
        mock_page = Mock()
        mock_page.get_text.return_value = "Sample medical text"
        mock_doc.__getitem__.return_value = mock_page
        
        mock_fitz.return_value = mock_doc
        
        processor = PDFProcessor()
        result = processor.extract_text_pymupdf(b"sample pdf content")
        
        assert result["success"] is True
        assert "full_text" in result
        assert result["metadata"]["extraction_method"] == "pymupdf"

class TestClinicalNLP:
    """Test cases for ClinicalNLPProcessor"""
    
    @patch('spacy.load')
    def test_nlp_processor_initialization(self, mock_spacy):
        """Test NLP processor initialization"""
        mock_nlp = Mock()
        mock_spacy.return_value = mock_nlp
        
        processor = ClinicalNLPProcessor()
        assert processor is not None
    
    def test_lab_value_extraction(self):
        """Test laboratory value extraction"""
        processor = ClinicalNLPProcessor()
        text = "Hemoglobin: 12.5 g/dl, Glucose: 95 mg/dl"
        
        lab_values = processor._extract_lab_values(text)
        
        assert len(lab_values) >= 2
        assert any("hemoglobin" in entity.text.lower() for entity in lab_values)
        assert any("glucose" in entity.text.lower() for entity in lab_values)
    
    def test_vital_signs_extraction(self):
        """Test vital signs extraction"""
        processor = ClinicalNLPProcessor()
        text = "Blood pressure: 120/80 mmHg, Heart rate: 72 bpm"
        
        vital_signs = processor._extract_vital_signs(text)
        
        assert len(vital_signs) >= 2
        assert any("blood pressure" in entity.text.lower() for entity in vital_signs)
        assert any("heart rate" in entity.text.lower() for entity in vital_signs)

class TestGeminiClient:
    """Test cases for GeminiClinicalClient"""
    
    @patch('google.generativeai.configure')
    @patch('google.generativeai.GenerativeModel')
    def test_gemini_client_initialization(self, mock_model, mock_configure):
        """Test Gemini client initialization"""
        mock_model_instance = Mock()
        mock_model.return_value = mock_model_instance
        
        client = GeminiClinicalClient()
        assert client is not None
    
    @patch('google.generativeai.configure')
    @patch('google.generativeai.GenerativeModel')
    def test_clinical_interpretation(self, mock_model, mock_configure):
        """Test clinical interpretation"""
        # Mock response
        mock_response = Mock()
        mock_response.text = json.dumps({
            "summary": "Test summary",
            "abnormalities": {"critical": [], "significant": [], "minor": []},
            "interpretation": "Test interpretation",
            "differential_diagnoses": ["Test diagnosis"],
            "recommendations": ["Test recommendation"],
            "follow_up_tests": ["Test follow-up"],
            "clinical_significance": "Test significance",
            "confidence_score": 0.8,
            "citations": []
        })
        
        mock_model_instance = Mock()
        mock_model_instance.generate_content.return_value = mock_response
        mock_model.return_value = mock_model_instance
        
        client = GeminiClinicalClient()
        result = client.interpret_clinical_data({"test": "data"})
        
        assert result.summary == "Test summary"
        assert result.confidence_score == 0.8

# Test fixtures and utilities
@pytest.fixture
def sample_medical_text():
    """Sample medical text for testing"""
    return """
    Patient: John Doe, 45-year-old male
    Chief Complaint: Chest pain
    
    History of Present Illness:
    Patient presents with acute onset chest pain, described as crushing, 
    radiating to left arm. Pain started 2 hours ago.
    
    Medications:
    - Aspirin 81mg daily
    - Metoprolol 50mg twice daily
    
    Vital Signs:
    Blood pressure: 140/90 mmHg
    Heart rate: 72 bpm
    Temperature: 98.6°F
    
    Laboratory Results:
    Cholesterol: 220 mg/dl
    Glucose: 95 mg/dl
    Hemoglobin: 12.5 g/dl
    
    Assessment and Plan:
    Chest pain, rule out acute coronary syndrome.
    Continue current medications.
    Order ECG and cardiac enzymes.
    """

if __name__ == "__main__":
    pytest.main([__file__])
